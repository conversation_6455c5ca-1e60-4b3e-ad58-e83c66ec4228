using BusSystem.Core.Entities;

namespace BusSystem.Core.Services;

/// <summary>
/// 实时数据服务接口
/// </summary>
public interface IRealtimeService
{
    /// <summary>
    /// 更新车辆位置
    /// </summary>
    Task<VehiclePosition> UpdateVehiclePositionAsync(VehiclePosition position);
    
    /// <summary>
    /// 批量更新车辆位置
    /// </summary>
    Task<IEnumerable<VehiclePosition>> UpdateVehiclePositionsAsync(IEnumerable<VehiclePosition> positions);
    
    /// <summary>
    /// 获取车辆最新位置
    /// </summary>
    Task<VehiclePosition?> GetVehicleLatestPositionAsync(int vehicleId);
    
    /// <summary>
    /// 获取线路上所有车辆的实时位置
    /// </summary>
    Task<IEnumerable<VehiclePosition>> GetLineRealtimePositionsAsync(int lineId);
    
    /// <summary>
    /// 获取附近的实时车辆
    /// </summary>
    Task<IEnumerable<VehiclePosition>> GetNearbyVehiclesAsync(double longitude, double latitude, double radiusMeters = 1000);
    
    /// <summary>
    /// 获取车辆轨迹
    /// </summary>
    Task<IEnumerable<VehiclePosition>> GetVehicleTrajectoryAsync(int vehicleId, DateTime startTime, DateTime endTime);
    
    /// <summary>
    /// 缓存车辆位置到Redis
    /// </summary>
    Task CacheVehiclePositionAsync(VehiclePosition position);
    
    /// <summary>
    /// 从Redis获取缓存的车辆位置
    /// </summary>
    Task<VehiclePosition?> GetCachedVehiclePositionAsync(int vehicleId);
    
    /// <summary>
    /// 清理过期的位置数据
    /// </summary>
    Task CleanupOldPositionDataAsync(TimeSpan retentionPeriod);
    
    /// <summary>
    /// 获取车辆实时状态
    /// </summary>
    Task<object> GetVehicleRealtimeStatusAsync(int vehicleId);
    
    /// <summary>
    /// 获取线路实时统计
    /// </summary>
    Task<object> GetLineRealtimeStatsAsync(int lineId);
}

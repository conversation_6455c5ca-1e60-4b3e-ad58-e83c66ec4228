﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.logging.abstractions\8.0.1\buildTransitive\net6.0\Microsoft.Extensions.Logging.Abstractions.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.logging.abstractions\8.0.1\buildTransitive\net6.0\Microsoft.Extensions.Logging.Abstractions.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.options\8.0.0\buildTransitive\net6.0\Microsoft.Extensions.Options.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.options\8.0.0\buildTransitive\net6.0\Microsoft.Extensions.Options.targets')" />
    <Import Project="$(NuGetPackageRoot)npgsql.entityframeworkcore.postgresql.nettopologysuite\8.0.4\build\netstandard2.0\Npgsql.EntityFrameworkCore.PostgreSQL.NetTopologySuite.targets" Condition="Exists('$(NuGetPackageRoot)npgsql.entityframeworkcore.postgresql.nettopologysuite\8.0.4\build\netstandard2.0\Npgsql.EntityFrameworkCore.PostgreSQL.NetTopologySuite.targets')" />
  </ImportGroup>
</Project>
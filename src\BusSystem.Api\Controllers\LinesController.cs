using Microsoft.AspNetCore.Mvc;
using BusSystem.Core.Services;
using BusSystem.Core.Entities;
using BusSystem.Shared.Models.Common;
using BusSystem.Shared.Constants;

namespace BusSystem.Api.Controllers;

/// <summary>
/// 公交线路控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class LinesController : ControllerBase
{
    private readonly ILineService _lineService;
    private readonly ILogger<LinesController> _logger;

    public LinesController(ILineService lineService, ILogger<LinesController> logger)
    {
        _lineService = lineService;
        _logger = logger;
    }

    /// <summary>
    /// 获取线路详情
    /// </summary>
    /// <param name="id">线路ID</param>
    [HttpGet("{id}")]
    public async Task<ActionResult<ApiResponse<BusLine>>> GetLine(int id)
    {
        try
        {
            var line = await _lineService.GetLineAsync(id);
            if (line == null)
            {
                return NotFound(ApiResponse<BusLine>.Fail("线路不存在"));
            }

            return Ok(ApiResponse<BusLine>.Ok(line));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取线路详情失败，线路ID: {LineId}", id);
            return StatusCode(500, ApiResponse<BusLine>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取线路及其站点信息
    /// </summary>
    /// <param name="id">线路ID</param>
    [HttpGet("{id}/stops")]
    public async Task<ActionResult<ApiResponse<BusLine>>> GetLineWithStops(int id)
    {
        try
        {
            var line = await _lineService.GetLineWithStopsAsync(id);
            if (line == null)
            {
                return NotFound(ApiResponse<BusLine>.Fail("线路不存在"));
            }

            return Ok(ApiResponse<BusLine>.Ok(line));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取线路及站点信息失败，线路ID: {LineId}", id);
            return StatusCode(500, ApiResponse<BusLine>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 搜索线路
    /// </summary>
    /// <param name="keyword">搜索关键词</param>
    [HttpGet("search")]
    public async Task<ActionResult<ApiResponse<IEnumerable<BusLine>>>> SearchLines([FromQuery] string? keyword)
    {
        try
        {
            var lines = await _lineService.SearchLinesAsync(keyword ?? string.Empty);
            return Ok(ApiResponse<IEnumerable<BusLine>>.Ok(lines));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "搜索线路失败，关键词: {Keyword}", keyword);
            return StatusCode(500, ApiResponse<IEnumerable<BusLine>>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 分页获取线路
    /// </summary>
    /// <param name="pageNumber">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="keyword">搜索关键词</param>
    [HttpGet]
    public async Task<ActionResult<ApiResponse<PagedResult<BusLine>>>> GetLines(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = SystemConstants.DefaultPageSize,
        [FromQuery] string? keyword = null)
    {
        try
        {
            // 验证分页参数
            if (pageNumber < 1) pageNumber = 1;
            if (pageSize < 1 || pageSize > SystemConstants.MaxPageSize) 
                pageSize = SystemConstants.DefaultPageSize;

            var result = await _lineService.GetLinesPagedAsync(pageNumber, pageSize, keyword);
            return Ok(ApiResponse<PagedResult<BusLine>>.Ok(result));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "分页获取线路失败，页码: {PageNumber}, 页大小: {PageSize}, 关键词: {Keyword}", 
                pageNumber, pageSize, keyword);
            return StatusCode(500, ApiResponse<PagedResult<BusLine>>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取活跃线路
    /// </summary>
    [HttpGet("active")]
    public async Task<ActionResult<ApiResponse<IEnumerable<BusLine>>>> GetActiveLines()
    {
        try
        {
            var lines = await _lineService.GetActiveLinesAsync();
            return Ok(ApiResponse<IEnumerable<BusLine>>.Ok(lines));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取活跃线路失败");
            return StatusCode(500, ApiResponse<IEnumerable<BusLine>>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 根据线路编号获取线路
    /// </summary>
    /// <param name="lineNumber">线路编号</param>
    /// <param name="direction">方向（可选）</param>
    [HttpGet("number/{lineNumber}")]
    public async Task<ActionResult<ApiResponse<BusLine>>> GetLineByNumber(
        string lineNumber, 
        [FromQuery] int? direction = null)
    {
        try
        {
            var line = await _lineService.GetLineByNumberAsync(lineNumber, direction);
            if (line == null)
            {
                return NotFound(ApiResponse<BusLine>.Fail("线路不存在"));
            }

            return Ok(ApiResponse<BusLine>.Ok(line));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据线路编号获取线路失败，线路编号: {LineNumber}, 方向: {Direction}", 
                lineNumber, direction);
            return StatusCode(500, ApiResponse<BusLine>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取线路统计信息
    /// </summary>
    /// <param name="id">线路ID</param>
    [HttpGet("{id}/stats")]
    public async Task<ActionResult<ApiResponse<object>>> GetLineStats(int id)
    {
        try
        {
            var stats = await _lineService.GetLineStatsAsync(id);
            return Ok(ApiResponse<object>.Ok(stats));
        }
        catch (InvalidOperationException ex)
        {
            return NotFound(ApiResponse<object>.Fail(ex.Message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取线路统计信息失败，线路ID: {LineId}", id);
            return StatusCode(500, ApiResponse<object>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 创建线路
    /// </summary>
    /// <param name="line">线路信息</param>
    [HttpPost]
    public async Task<ActionResult<ApiResponse<BusLine>>> CreateLine([FromBody] BusLine line)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<BusLine>.Fail("请求参数无效"));
            }

            var result = await _lineService.CreateLineAsync(line);
            return CreatedAtAction(nameof(GetLine), new { id = result.Id }, 
                ApiResponse<BusLine>.Ok(result, "线路创建成功"));
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ApiResponse<BusLine>.Fail(ex.Message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建线路失败，线路编号: {LineNumber}", line.LineNumber);
            return StatusCode(500, ApiResponse<BusLine>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 更新线路
    /// </summary>
    /// <param name="id">线路ID</param>
    /// <param name="line">线路信息</param>
    [HttpPut("{id}")]
    public async Task<ActionResult<ApiResponse<BusLine>>> UpdateLine(int id, [FromBody] BusLine line)
    {
        try
        {
            if (id != line.Id)
            {
                return BadRequest(ApiResponse<BusLine>.Fail("线路ID不匹配"));
            }

            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<BusLine>.Fail("请求参数无效"));
            }

            var result = await _lineService.UpdateLineAsync(line);
            return Ok(ApiResponse<BusLine>.Ok(result, "线路更新成功"));
        }
        catch (InvalidOperationException ex)
        {
            return NotFound(ApiResponse<BusLine>.Fail(ex.Message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新线路失败，线路ID: {LineId}", id);
            return StatusCode(500, ApiResponse<BusLine>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 删除线路
    /// </summary>
    /// <param name="id">线路ID</param>
    [HttpDelete("{id}")]
    public async Task<ActionResult<ApiResponse>> DeleteLine(int id)
    {
        try
        {
            await _lineService.DeleteLineAsync(id);
            return Ok(ApiResponse.Ok("线路删除成功"));
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ApiResponse.Fail(ex.Message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除线路失败，线路ID: {LineId}", id);
            return StatusCode(500, ApiResponse.Fail("服务器内部错误"));
        }
    }
}

using BusSystem.Shared.Enums;
using BusSystem.Shared.Models.Common;

namespace BusSystem.Core.Services;

/// <summary>
/// 坐标系转换服务接口
/// </summary>
public interface ICoordinateTransformService
{
    /// <summary>
    /// WGS84转GCJ02（火星坐标）
    /// </summary>
    (double longitude, double latitude) Wgs84ToGcj02(double longitude, double latitude);
    
    /// <summary>
    /// GCJ02转WGS84
    /// </summary>
    (double longitude, double latitude) Gcj02ToWgs84(double longitude, double latitude);
    
    /// <summary>
    /// GCJ02转BD09（百度坐标）
    /// </summary>
    (double longitude, double latitude) Gcj02ToBd09(double longitude, double latitude);
    
    /// <summary>
    /// BD09转GCJ02
    /// </summary>
    (double longitude, double latitude) Bd09ToGcj02(double longitude, double latitude);
    
    /// <summary>
    /// WGS84转BD09
    /// </summary>
    (double longitude, double latitude) Wgs84ToBd09(double longitude, double latitude);
    
    /// <summary>
    /// BD09转WGS84
    /// </summary>
    (double longitude, double latitude) Bd09ToWgs84(double longitude, double latitude);
    
    /// <summary>
    /// 根据地图类型转换坐标
    /// </summary>
    (double longitude, double latitude) TransformForMapType(double longitude, double latitude, 
        CoordinateSystem from, CoordinateSystem to);
    
    /// <summary>
    /// 批量坐标转换
    /// </summary>
    IEnumerable<(double longitude, double latitude)> BatchTransform(
        IEnumerable<(double longitude, double latitude)> coordinates,
        CoordinateSystem from, CoordinateSystem to);
    
    /// <summary>
    /// 判断坐标是否在中国境内
    /// </summary>
    bool IsInChina(double longitude, double latitude);

    /// <summary>
    /// 从WGS84坐标创建多坐标系位置响应
    /// </summary>
    LocationResponse CreateLocationResponse(double longitude, double latitude);
}

using BusSystem.Core.Entities;
using NetTopologySuite.Geometries;

namespace BusSystem.Core.Interfaces;

/// <summary>
/// 公交站点Repository接口
/// </summary>
public interface IBusStopRepository : IRepository<BusStop>
{
    /// <summary>
    /// 根据站点编码获取站点
    /// </summary>
    Task<BusStop?> GetByStopCodeAsync(string stopCode);
    
    /// <summary>
    /// 根据站点名称搜索站点
    /// </summary>
    Task<IEnumerable<BusStop>> SearchByNameAsync(string name);
    
    /// <summary>
    /// 获取附近的站点（使用PostGIS）
    /// </summary>
    Task<IEnumerable<BusStop>> GetNearbyStopsAsync(double longitude, double latitude, double radiusMeters);
    
    /// <summary>
    /// 获取站点及其线路信息
    /// </summary>
    Task<BusStop?> GetWithLinesAsync(int stopId);
    
    /// <summary>
    /// 根据区域获取站点
    /// </summary>
    Task<IEnumerable<BusStop>> GetByDistrictAsync(string district);
    
    /// <summary>
    /// 获取指定类型的站点
    /// </summary>
    Task<IEnumerable<BusStop>> GetByTypeAsync(int stopType);
    
    /// <summary>
    /// 获取活跃的站点
    /// </summary>
    Task<IEnumerable<BusStop>> GetActiveAsync();
}

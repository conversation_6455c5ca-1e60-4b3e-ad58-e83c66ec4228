using NetTopologySuite.Geometries;

namespace BusSystem.Core.Entities;

/// <summary>
/// 公交站点实体
/// </summary>
public class BusStop : BaseEntity
{
    /// <summary>
    /// 站点名称
    /// </summary>
    public string StopName { get; set; } = string.Empty;
    
    /// <summary>
    /// 站点编码
    /// </summary>
    public string? StopCode { get; set; }
    
    /// <summary>
    /// 站点位置（PostGIS Point）
    /// </summary>
    public Point Location { get; set; } = null!;
    
    /// <summary>
    /// 地址
    /// </summary>
    public string? Address { get; set; }
    
    /// <summary>
    /// 所属区域
    /// </summary>
    public string? District { get; set; }
    
    /// <summary>
    /// 站点设施信息（JSON格式）
    /// </summary>
    public string? Facilities { get; set; }
    
    /// <summary>
    /// 站点类型：1-普通站，2-枢纽站，3-首末站
    /// </summary>
    public int StopType { get; set; } = 1;
    
    /// <summary>
    /// 状态：0-停用，1-正常
    /// </summary>
    public int Status { get; set; } = 1;
    
    /// <summary>
    /// 经度（冗余字段，便于查询）
    /// </summary>
    public double Longitude => Location?.X ?? 0;
    
    /// <summary>
    /// 纬度（冗余字段，便于查询）
    /// </summary>
    public double Latitude => Location?.Y ?? 0;
    
    /// <summary>
    /// 线路站点关联
    /// </summary>
    public virtual ICollection<LineStop> LineStops { get; set; } = new List<LineStop>();
}

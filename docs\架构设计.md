# 实时公交系统架构设计文档

## 1. 系统架构概述

### 1.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   H5前端应用    │    │   运营后台      │    │   第三方服务    │
│   (Vue.js)      │    │   (Vue.js)      │    │   (高德地图)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   API网关       │
                    │   (Nginx)       │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   后端服务      │
                    │   (Node.js)     │
                    └─────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Redis缓存     │    │   MySQL数据库   │    │   公交数据接口  │
│   (实时数据)    │    │   (基础数据)    │    │   (第三方)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 1.2 技术栈选型

#### 前端技术栈
- **框架**: Vue.js 3 + Composition API
- **UI组件**: Vant 4 (移动端UI库)
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **HTTP客户端**: Axios
- **地图**: 高德地图JavaScript API
- **构建工具**: Vite
- **语言**: TypeScript

#### 后端技术栈
- **运行环境**: Node.js 18+
- **框架**: Express.js
- **语言**: TypeScript
- **数据库**:
  - PostgreSQL 15 + PostGIS (基础数据，地理空间查询)
  - TimescaleDB (GPS轨迹等时序数据)
- **缓存**:
  - Redis Geo (高频地理位置查询)
  - Redis (通用缓存、会话管理)
- **ORM**: Prisma (支持PostgreSQL)
- **API文档**: Swagger/OpenAPI
- **日志**: Winston
- **进程管理**: PM2

#### 运营后台技术栈
- **框架**: Vue.js 3 + Element Plus
- **状态管理**: Pinia
- **图表**: ECharts
- **构建工具**: Vite
- **语言**: TypeScript

#### 部署技术栈
- **容器化**: Docker + Docker Compose
- **反向代理**: Nginx
- **数据库集群**: PostgreSQL主从 + TimescaleDB集群
- **缓存集群**: Redis Cluster
- **监控**: Prometheus + Grafana
- **日志收集**: ELK Stack

## 2. 系统模块设计

### 2.1 前端模块架构

```
src/
├── components/          # 公共组件
│   ├── BusLine/        # 线路相关组件
│   ├── BusStop/        # 站点相关组件
│   ├── Map/            # 地图相关组件
│   └── Common/         # 通用组件
├── views/              # 页面组件
│   ├── Home/           # 首页
│   ├── LineDetail/     # 线路详情
│   ├── StopDetail/     # 站点详情
│   └── Search/         # 搜索页
├── stores/             # 状态管理
│   ├── location.ts     # 位置信息
│   ├── busData.ts      # 公交数据
│   └── user.ts         # 用户信息
├── services/           # API服务
│   ├── api.ts          # API配置
│   ├── busService.ts   # 公交数据服务
│   └── mapService.ts   # 地图服务
├── utils/              # 工具函数
│   ├── location.ts     # 位置工具
│   ├── time.ts         # 时间工具
│   └── format.ts       # 格式化工具
└── types/              # TypeScript类型定义
    ├── bus.ts          # 公交相关类型
    └── map.ts          # 地图相关类型
```

### 2.2 后端模块架构

```
src/
├── controllers/        # 控制器层
│   ├── busController.ts
│   ├── stopController.ts
│   └── searchController.ts
├── services/           # 业务逻辑层
│   ├── busService.ts
│   ├── locationService.ts
│   └── cacheService.ts
├── models/             # 数据模型
│   ├── Bus.ts
│   ├── Stop.ts
│   └── Line.ts
├── middleware/         # 中间件
│   ├── auth.ts
│   ├── rateLimit.ts
│   └── errorHandler.ts
├── utils/              # 工具函数
│   ├── logger.ts
│   ├── validator.ts
│   └── calculator.ts
├── config/             # 配置文件
│   ├── database.ts
│   ├── redis.ts
│   └── app.ts
└── routes/             # 路由定义
    ├── api.ts
    └── index.ts
```

## 3. 数据库设计

### 3.1 核心数据表（PostgreSQL + PostGIS）

#### 线路表 (bus_lines)
```sql
CREATE TABLE bus_lines (
    id SERIAL PRIMARY KEY,
    line_number VARCHAR(20) NOT NULL,
    line_name VARCHAR(100) NOT NULL,
    start_stop VARCHAR(100) NOT NULL,
    end_stop VARCHAR(100) NOT NULL,
    direction SMALLINT NOT NULL, -- 0:上行 1:下行
    route_geometry GEOMETRY(LINESTRING, 4326), -- 线路几何形状
    operation_time VARCHAR(50),
    ticket_price DECIMAL(4,2),
    total_distance DECIMAL(8,2),
    total_stops INTEGER,
    status SMALLINT DEFAULT 1, -- 0:停运 1:正常
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建空间索引
CREATE INDEX idx_lines_geometry ON bus_lines USING GIST (route_geometry);
```

#### 站点表 (bus_stops)
```sql
CREATE TABLE bus_stops (
    id SERIAL PRIMARY KEY,
    stop_name VARCHAR(100) NOT NULL,
    stop_code VARCHAR(20) UNIQUE,
    location GEOMETRY(POINT, 4326) NOT NULL, -- 使用PostGIS几何类型
    address VARCHAR(200),
    facilities JSONB, -- 站点设施信息，使用JSONB
    status SMALLINT DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建空间索引
CREATE INDEX idx_stops_location ON bus_stops USING GIST (location);
```

#### 线路站点关联表 (line_stops)
```sql
CREATE TABLE line_stops (
    id INT PRIMARY KEY AUTO_INCREMENT,
    line_id INT NOT NULL,
    stop_id INT NOT NULL,
    sequence_number INT NOT NULL, -- 站点顺序
    distance_from_start DECIMAL(8,2), -- 距离起点距离
    FOREIGN KEY (line_id) REFERENCES bus_lines(id),
    FOREIGN KEY (stop_id) REFERENCES bus_stops(id),
    UNIQUE KEY unique_line_stop (line_id, stop_id, sequence_number)
);
```

#### 车辆表 (buses)
```sql
CREATE TABLE buses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    bus_number VARCHAR(20) NOT NULL,
    line_id INT NOT NULL,
    license_plate VARCHAR(20),
    capacity INT,
    status TINYINT DEFAULT 1, -- 0:停运 1:运营中 2:维修
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (line_id) REFERENCES bus_lines(id)
);
```

### 3.2 Redis Geo + 缓存设计

#### Redis Geo 地理位置存储
```bash
# 站点地理位置（永久存储）
GEOADD geo:bus_stops 114.085456 22.547123 "stop:1001"

# 车辆实时位置（300秒TTL）
GEOADD geo:buses:2001 114.085456 22.547123 "bus:3001"

# 附近站点查询（微秒级响应）
GEORADIUS geo:bus_stops 114.085456 22.547123 500 m WITHDIST ASC COUNT 10
```

#### 通用缓存键命名规范
```
bus:realtime:{line_id}:{direction}     # 线路实时数据
bus:arrival:{stop_id}:{line_id}        # 站点到站预测
line:info:{line_id}                    # 线路基础信息
stop:info:{stop_id}                    # 站点基础信息
search:line:{keyword_hash}             # 线路搜索结果
```

#### 缓存策略
- **Redis Geo**: 站点永久存储，车辆位置300秒TTL
- **实时数据**: TTL 30秒
- **基础数据**: TTL 1小时
- **搜索结果**: TTL 10分钟
- **用户位置**: TTL 5分钟

## 4. API接口设计

### 4.1 RESTful API规范

#### 基础路径
```
/api/v1/
├── /stops              # 站点相关
├── /lines              # 线路相关
├── /buses              # 车辆相关
├── /search             # 搜索相关
└── /location           # 位置相关
```

#### 核心接口

**获取附近站点**
```
GET /api/v1/stops/nearby?lat={lat}&lng={lng}&radius={radius}
Response: {
  "code": 200,
  "data": {
    "stops": [
      {
        "id": 1,
        "name": "人民广场",
        "distance": 120,
        "lines": [
          {
            "lineNumber": "1路",
            "direction": "上行",
            "nextBus": {
              "arrivalTime": "2分钟",
              "stopsLeft": 3
            }
          }
        ]
      }
    ]
  }
}
```

**获取线路详情**
```
GET /api/v1/lines/{lineId}?direction={direction}
Response: {
  "code": 200,
  "data": {
    "lineInfo": {
      "id": 1,
      "lineNumber": "1路",
      "startStop": "火车站",
      "endStop": "机场",
      "operationTime": "06:00-22:00"
    },
    "stops": [...],
    "realtime": {
      "buses": [
        {
          "busId": "001",
          "currentStop": 5,
          "nextStopArrival": "3分钟",
          "crowdLevel": "适中"
        }
      ]
    }
  }
}
```

### 4.2 WebSocket实时推送

#### 连接管理
```javascript
// 客户端连接
const ws = new WebSocket('wss://api.example.com/ws');

// 订阅线路实时数据
ws.send(JSON.stringify({
  type: 'subscribe',
  channel: 'line_realtime',
  lineId: 1,
  direction: 0
}));

// 接收实时数据
ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  if (data.type === 'line_realtime_update') {
    // 更新界面数据
  }
};
```

## 5. 性能优化策略

### 5.1 前端优化
- **代码分割**: 路由级别的懒加载
- **资源优化**: 图片压缩、CDN加速
- **缓存策略**: Service Worker缓存
- **首屏优化**: 骨架屏、预加载

### 5.2 后端优化
- **数据库优化**: 索引优化、查询优化
- **缓存策略**: 多级缓存、缓存预热
- **接口优化**: 数据聚合、批量查询
- **负载均衡**: 水平扩展、读写分离

### 5.3 系统监控
- **性能监控**: 响应时间、吞吐量
- **错误监控**: 异常捕获、错误统计
- **业务监控**: 用户行为、功能使用率
- **基础监控**: CPU、内存、磁盘

## 6. 安全设计

### 6.1 数据安全
- **传输加密**: HTTPS/WSS
- **数据脱敏**: 敏感信息处理
- **访问控制**: API访问限制
- **输入验证**: 参数校验、SQL注入防护

### 6.2 系统安全
- **认证授权**: JWT Token
- **防护措施**: 防DDoS、防爬虫
- **日志审计**: 操作日志记录
- **备份恢复**: 数据备份策略

## 7. 部署架构

### 7.1 容器化部署
```yaml
# docker-compose.yml
version: '3.8'
services:
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    
  frontend:
    build: ./frontend
    
  backend:
    build: ./backend
    environment:
      - NODE_ENV=production
    
  redis:
    image: redis:alpine
    
  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=password
```

### 7.2 生产环境架构
```
Internet
    │
┌───▼───┐
│  CDN  │
└───┬───┘
    │
┌───▼───┐
│ WAF   │
└───┬───┘
    │
┌───▼───┐
│ LB    │
└───┬───┘
    │
┌───▼───┐
│ Nginx │
└───┬───┘
    │
┌───▼───┐
│ App   │ × N
└───┬───┘
    │
┌───▼───┐
│ Cache │
└───┬───┘
    │
┌───▼───┐
│  DB   │
└───────┘
```

这个架构设计文档涵盖了系统的技术架构、模块设计、数据库设计、API设计、性能优化和安全考虑等方面。接下来我们可以根据这个架构开始具体的开发工作。

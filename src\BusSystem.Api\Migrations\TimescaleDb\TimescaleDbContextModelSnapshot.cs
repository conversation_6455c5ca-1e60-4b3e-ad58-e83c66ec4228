﻿// <auto-generated />
using System;
using BusSystem.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace BusSystem.Api.Migrations.TimescaleDb
{
    [DbContext(typeof(TimescaleDbContext))]
    partial class TimescaleDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.8")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("BusSystem.Core.Entities.VehiclePosition", b =>
                {
                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("timestamp");

                    b.Property<int>("VehicleId")
                        .HasColumnType("integer")
                        .HasColumnName("vehicle_id");

                    b.Property<double?>("Accuracy")
                        .HasColumnType("double precision")
                        .HasColumnName("accuracy");

                    b.Property<int?>("CurrentStopId")
                        .HasColumnType("integer")
                        .HasColumnName("current_stop_id");

                    b.Property<double?>("Direction")
                        .HasColumnType("double precision")
                        .HasColumnName("direction");

                    b.Property<double>("Latitude")
                        .HasColumnType("double precision")
                        .HasColumnName("latitude");

                    b.Property<double>("Longitude")
                        .HasColumnType("double precision")
                        .HasColumnName("longitude");

                    b.Property<int?>("NextStopId")
                        .HasColumnType("integer")
                        .HasColumnName("next_stop_id");

                    b.Property<double?>("Speed")
                        .HasColumnType("double precision")
                        .HasColumnName("speed");

                    b.Property<int>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1)
                        .HasColumnName("status");

                    b.HasKey("Timestamp", "VehicleId");

                    b.HasIndex("Timestamp")
                        .HasDatabaseName("idx_vehicle_positions_timestamp");

                    b.HasIndex("VehicleId")
                        .HasDatabaseName("idx_vehicle_positions_vehicle_id");

                    b.HasIndex("VehicleId", "Timestamp")
                        .HasDatabaseName("idx_vehicle_positions_vehicle_time");

                    b.ToTable("vehicle_positions", (string)null);
                });
#pragma warning restore 612, 618
        }
    }
}

using Microsoft.AspNetCore.Mvc;
using NetTopologySuite.Geometries;
using BusSystem.Core.Services;
using BusSystem.Core.Entities;
using BusSystem.Shared.Models.Common;

namespace BusSystem.Api.Controllers;

/// <summary>
/// 数据管理控制器（用于测试和初始化）
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class DataController : ControllerBase
{
    private readonly ILineService _lineService;
    private readonly IStopService _stopService;
    private readonly IRealtimeService _realtimeService;
    private readonly ILogger<DataController> _logger;

    public DataController(
        ILineService lineService,
        IStopService stopService,
        IRealtimeService realtimeService,
        ILogger<DataController> logger)
    {
        _lineService = lineService;
        _stopService = stopService;
        _realtimeService = realtimeService;
        _logger = logger;
    }

    /// <summary>
    /// 初始化测试数据
    /// </summary>
    [HttpPost("init-test-data")]
    public async Task<ActionResult<ApiResponse>> InitTestData()
    {
        try
    {
            var geometryFactory = new GeometryFactory(new PrecisionModel(), 4326);

            // 创建测试站点
            var stops = new List<BusStop>
            {
                new BusStop
                {
                    StopName = "天安门东",
                    StopCode = "001",
                    Location = geometryFactory.CreatePoint(new Coordinate(116.3974, 39.9093)),
                    Address = "北京市东城区天安门东",
                    District = "东城区",
                    StopType = 2,
                    Status = 1
                },
                new BusStop
                {
                    StopName = "王府井",
                    StopCode = "002", 
                    Location = geometryFactory.CreatePoint(new Coordinate(116.4074, 39.9093)),
                    Address = "北京市东城区王府井大街",
                    District = "东城区",
                    StopType = 1,
                    Status = 1
                },
                new BusStop
                {
                    StopName = "东单",
                    StopCode = "003",
                    Location = geometryFactory.CreatePoint(new Coordinate(116.4174, 39.9093)),
                    Address = "北京市东城区东单",
                    District = "东城区",
                    StopType = 1,
                    Status = 1
                }
            };

            foreach (var stop in stops)
            {
                await _stopService.CreateStopAsync(stop);
            }

            // 创建测试线路
            var line = new BusLine
            {
                LineNumber = "1",
                LineName = "1路",
                StartStopName = "天安门东",
                EndStopName = "东单",
                Direction = 0,
                OperationStartTime = new TimeSpan(5, 30, 0),
                OperationEndTime = new TimeSpan(23, 0, 0),
                PeakInterval = 5,
                NormalInterval = 10,
                TicketPrice = 2.0m,
                TotalDistance = 5.2m,
                TotalStops = 3,
                Status = 1
            };

            var createdLine = await _lineService.CreateLineAsync(line);

            // 创建测试车辆位置数据
            var positions = new List<VehiclePosition>
            {
                new VehiclePosition
                {
                    VehicleId = 1,
                    Timestamp = DateTime.UtcNow.AddMinutes(-5),
                    Latitude = 39.9093,
                    Longitude = 116.3974,
                    Speed = 25.5,
                    Direction = 90,
                    Status = 1
                },
                new VehiclePosition
                {
                    VehicleId = 1,
                    Timestamp = DateTime.UtcNow.AddMinutes(-3),
                    Latitude = 39.9093,
                    Longitude = 116.4024,
                    Speed = 30.0,
                    Direction = 90,
                    Status = 1
                },
                new VehiclePosition
                {
                    VehicleId = 1,
                    Timestamp = DateTime.UtcNow,
                    Latitude = 39.9093,
                    Longitude = 116.4074,
                    Speed = 28.5,
                    Direction = 90,
                    Status = 1
                }
            };

            await _realtimeService.UpdateVehiclePositionsAsync(positions);

            _logger.LogInformation("测试数据初始化完成");

            return Ok(ApiResponse.Ok("测试数据初始化成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "初始化测试数据失败");
            return StatusCode(500, ApiResponse.Fail("初始化测试数据失败: " + ex.Message));
        }
    }

    /// <summary>
    /// 清理测试数据
    /// </summary>
    [HttpDelete("clear-test-data")]
    public async Task<ActionResult<ApiResponse>> ClearTestData()
    {
        try
        {
            // 清理位置数据
            await _realtimeService.CleanupOldPositionDataAsync(TimeSpan.Zero);

            _logger.LogInformation("测试数据清理完成");
            return Ok(ApiResponse.Ok("测试数据清理成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理测试数据失败");
            return StatusCode(500, ApiResponse.Fail("清理测试数据失败: " + ex.Message));
        }
    }

    /// <summary>
    /// 模拟车辆位置更新
    /// </summary>
    [HttpPost("simulate-vehicle-movement")]
    public async Task<ActionResult<ApiResponse>> SimulateVehicleMovement([FromQuery] int vehicleId = 1)
    {
        try
        {
            var random = new Random();
            var baseLatitude = 39.9093;
            var baseLongitude = 116.3974;

            var position = new VehiclePosition
            {
                VehicleId = vehicleId,
                Timestamp = DateTime.UtcNow,
                Latitude = baseLatitude + (random.NextDouble() - 0.5) * 0.01,
                Longitude = baseLongitude + (random.NextDouble() - 0.5) * 0.01,
                Speed = 20 + random.NextDouble() * 20,
                Direction = random.NextDouble() * 360,
                Status = 1
            };

            await _realtimeService.UpdateVehiclePositionAsync(position);

            return Ok(ApiResponse.Ok("车辆位置模拟更新成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "模拟车辆位置更新失败");
            return StatusCode(500, ApiResponse.Fail("模拟车辆位置更新失败: " + ex.Message));
        }
    }
}

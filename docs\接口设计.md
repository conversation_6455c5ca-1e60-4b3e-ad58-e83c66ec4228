# 实时公交系统API接口设计文档

## 1. API设计概述

### 1.1 设计原则
- **RESTful设计**: 遵循REST架构风格
- **版本控制**: 通过URL路径进行版本管理
- **统一响应**: 标准化的响应格式
- **错误处理**: 完善的错误码和错误信息
- **安全性**: 接口访问限制和数据验证

### 1.2 基础信息
- **Base URL**: `https://api.realtimebus.com/api/v1`
- **协议**: HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8
- **时区**: UTC+8 (北京时间)

### 1.3 通用响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": 1703123456789,
  "requestId": "req_123456789"
}
```

### 1.4 状态码规范
```
200 - 成功
400 - 请求参数错误
401 - 未授权访问
403 - 禁止访问
404 - 资源不存在
429 - 请求频率超限
500 - 服务器内部错误
503 - 服务不可用
```

## 2. 认证和限流

### 2.1 API Key认证
```http
GET /api/v1/stops/nearby
Authorization: Bearer your_api_key
```

### 2.2 请求限流
- **普通用户**: 1000次/小时
- **高级用户**: 10000次/小时
- **企业用户**: 无限制

## 3. 核心API接口

### 3.1 位置相关接口

#### 3.1.1 获取附近站点
```http
GET /api/v1/stops/nearby
```

**请求参数**:
```json
{
  "lat": 22.547,          // 必填，纬度
  "lng": 114.085,         // 必填，经度
  "radius": 500,          // 可选，搜索半径(米)，默认500
  "limit": 10             // 可选，返回数量限制，默认10
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "stops": [
      {
        "stopId": 1001,
        "stopName": "大中华国际广场",
        "distance": 120,
        "latitude": 22.547123,
        "longitude": 114.085456,
        "address": "深圳市福田区福华三路",
        "geoSource": "redis_geo", // 标识数据来源
        "lines": [
          {
            "lineId": 2001,
            "lineNumber": "64路",
            "direction": 0,
            "startStop": "火车站",
            "endStop": "机场",
            "arrivals": [
              {
                "arrivalTime": "8分钟",
                "stopsRemaining": 2,
                "busId": 3001,
                "crowdLevel": "适中",
                "predictionMethod": "hybrid" // 预测方法
              },
              {
                "arrivalTime": "15分钟",
                "stopsRemaining": 5,
                "busId": 3002,
                "crowdLevel": "拥挤",
                "predictionMethod": "realtime"
              }
            ]
          }
        ]
      }
    ],
    "total": 5,
    "queryTime": 2, // 查询耗时(毫秒)
    "cacheHit": true // 是否命中缓存
  }
}
```

### 3.2 线路相关接口

#### 3.2.1 获取线路详情
```http
GET /api/v1/lines/{lineId}
```

**请求参数**:
```json
{
  "direction": 0          // 可选，方向 0:上行 1:下行，默认0
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "lineInfo": {
      "lineId": 2001,
      "lineNumber": "64路",
      "lineName": "火车站-机场",
      "direction": 0,
      "startStop": "火车站",
      "endStop": "机场",
      "operationTime": "06:00-22:00",
      "peakInterval": 5,
      "normalInterval": 8,
      "ticketPrice": 2.00,
      "totalDistance": 25.6,
      "totalStops": 18,
      "status": 1
    },
    "stops": [
      {
        "stopId": 1001,
        "stopName": "火车站",
        "sequence": 1,
        "latitude": 22.547,
        "longitude": 114.085,
        "distanceFromStart": 0,
        "estimatedTime": 0
      }
    ],
    "realtime": {
      "buses": [
        {
          "busId": 3001,
          "busNumber": "粤B12345",
          "currentStopId": 1005,
          "currentStopName": "福华三路口",
          "nextStopId": 1006,
          "nextStopName": "会展中心",
          "latitude": 22.547,
          "longitude": 114.085,
          "speed": 25.5,
          "passengerCount": 35,
          "capacity": 50,
          "crowdLevel": "适中",
          "status": 1,
          "gpsAccuracy": 5.2,
          "dataSource": "timescaledb",
          "lastUpdateTime": "2025-01-19T11:47:30+08:00"
        }
      ]
    }
  }
}
```

#### 3.2.2 获取线路实时信息
```http
GET /api/v1/lines/{lineId}/realtime
```

**请求参数**:
```json
{
  "direction": 0,         // 可选，方向
  "stopId": 1001         // 可选，特定站点ID
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "lineId": 2001,
    "direction": 0,
    "buses": [
      {
        "busId": 3001,
        "position": {
          "latitude": 22.547,
          "longitude": 114.085,
          "currentStopId": 1005,
          "nextStopId": 1006
        },
        "predictions": [
          {
            "stopId": 1006,
            "stopName": "会展中心",
            "arrivalTime": "2025-01-19T11:49:30+08:00",
            "stopsRemaining": 1,
            "confidence": 0.95
          }
        ]
      }
    ],
    "lastUpdateTime": "2025-01-19T11:47:30+08:00"
  }
}
```

### 3.3 站点相关接口

#### 3.3.1 获取站点信息
```http
GET /api/v1/stops/{stopId}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "stopInfo": {
      "stopId": 1001,
      "stopName": "大中华国际广场",
      "stopCode": "SZ001001",
      "latitude": 22.547,
      "longitude": 114.085,
      "address": "深圳市福田区福华三路",
      "district": "福田区",
      "facilities": {
        "shelter": true,
        "bench": true,
        "display": true,
        "wifi": false
      },
      "stopType": 1,
      "status": 1
    },
    "lines": [
      {
        "lineId": 2001,
        "lineNumber": "64路",
        "direction": 0,
        "startStop": "火车站",
        "endStop": "机场",
        "sequence": 8,
        "arrivals": [
          {
            "arrivalTime": "6分钟",
            "stopsRemaining": 2,
            "busId": 3001,
            "crowdLevel": "适中"
          }
        ]
      }
    ],
    "nearbyStops": [
      {
        "stopId": 1002,
        "stopName": "大中华国际广场(对面)",
        "distance": 50,
        "direction": "对向"
      }
    ]
  }
}
```

#### 3.3.2 获取站点到站预测
```http
GET /api/v1/stops/{stopId}/arrivals
```

**请求参数**:
```json
{
  "lineId": 2001,        // 可选，特定线路ID
  "limit": 5             // 可选，返回数量限制
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "stopId": 1001,
    "stopName": "大中华国际广场",
    "arrivals": [
      {
        "lineId": 2001,
        "lineNumber": "64路",
        "direction": 0,
        "busId": 3001,
        "arrivalTime": "2025-01-19T11:53:30+08:00",
        "arrivalMinutes": 6,
        "stopsRemaining": 2,
        "crowdLevel": "适中",
        "confidence": 0.92,
        "status": "正常"
      }
    ],
    "lastUpdateTime": "2025-01-19T11:47:30+08:00"
  }
}
```

### 3.4 搜索相关接口

#### 3.4.1 综合搜索
```http
GET /api/v1/search
```

**请求参数**:
```json
{
  "keyword": "64路",      // 必填，搜索关键词
  "type": "all",          // 可选，搜索类型 all|line|stop
  "limit": 10             // 可选，返回数量限制
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "lines": [
      {
        "lineId": 2001,
        "lineNumber": "64路",
        "lineName": "火车站-机场",
        "startStop": "火车站",
        "endStop": "机场",
        "status": 1
      }
    ],
    "stops": [
      {
        "stopId": 1001,
        "stopName": "大中华国际广场",
        "address": "深圳市福田区福华三路",
        "lineCount": 8
      }
    ],
    "total": {
      "lines": 1,
      "stops": 1
    }
  }
}
```

#### 3.4.2 线路搜索
```http
GET /api/v1/search/lines
```

#### 3.4.3 站点搜索
```http
GET /api/v1/search/stops
```

### 3.5 系统相关接口

#### 3.5.1 系统状态
```http
GET /api/v1/system/status
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "status": "healthy",
    "version": "1.0.0",
    "uptime": 86400,
    "services": {
      "database": "healthy",
      "redis": "healthy",
      "gps": "healthy"
    },
    "statistics": {
      "totalLines": 245,
      "totalStops": 3456,
      "activeBuses": 1234,
      "lastDataUpdate": "2025-01-19T11:47:30+08:00"
    }
  }
}
```

## 4. WebSocket实时推送

### 4.1 连接建立
```javascript
const ws = new WebSocket('wss://api.realtimebus.com/ws/v1');
```

### 4.2 订阅消息格式
```json
{
  "type": "subscribe",
  "channel": "line_realtime",
  "params": {
    "lineId": 2001,
    "direction": 0
  }
}
```

### 4.3 推送消息格式
```json
{
  "type": "line_realtime_update",
  "channel": "line_realtime",
  "data": {
    "lineId": 2001,
    "direction": 0,
    "buses": [
      {
        "busId": 3001,
        "position": {
          "latitude": 22.547,
          "longitude": 114.085
        },
        "predictions": []
      }
    ],
    "timestamp": 1703123456789
  }
}
```

### 4.4 支持的订阅频道
- `line_realtime`: 线路实时数据
- `stop_arrivals`: 站点到站信息
- `bus_position`: 车辆位置更新
- `system_alerts`: 系统告警信息

## 5. 错误处理

### 5.1 错误响应格式
```json
{
  "code": 400,
  "message": "Invalid parameters",
  "error": {
    "type": "VALIDATION_ERROR",
    "details": [
      {
        "field": "lat",
        "message": "Latitude must be between -90 and 90"
      }
    ]
  },
  "timestamp": 1703123456789,
  "requestId": "req_123456789"
}
```

### 5.2 常见错误码
```
1001 - 参数缺失
1002 - 参数格式错误
1003 - 参数值超出范围
2001 - 线路不存在
2002 - 站点不存在
2003 - 车辆不存在
3001 - 数据暂时不可用
3002 - 服务维护中
4001 - API密钥无效
4002 - 请求频率超限
5001 - 内部服务错误
5002 - 数据库连接失败
```

## 6. 接口性能要求

### 6.1 响应时间要求（优化后）
- **附近站点查询**: < 5ms (Redis Geo优化)
- **基础数据查询**: < 200ms (PostgreSQL + 缓存)
- **实时数据查询**: < 100ms (Redis + TimescaleDB)
- **搜索接口**: < 500ms (优化后)
- **复杂地理查询**: < 1s (PostGIS优化)

### 6.2 并发处理能力
- 支持2000+ QPS (架构优化后)
- WebSocket连接数: 20000+
- 数据更新频率: 10-30秒
- Redis Geo查询: 10000+ QPS

### 6.3 数据库性能指标
- **PostgreSQL**: 读取QPS 5000+, 写入TPS 1000+
- **TimescaleDB**: 时序数据写入 10000+ TPS
- **Redis Geo**: 地理查询 10000+ QPS
- **Redis Cache**: 通用缓存 50000+ QPS

## 7. 接口测试

### 7.1 测试环境
- **测试地址**: `https://test-api.realtimebus.com/api/v1`
- **测试密钥**: `test_key_123456`

### 7.2 Postman集合
提供完整的Postman测试集合，包含所有接口的测试用例。

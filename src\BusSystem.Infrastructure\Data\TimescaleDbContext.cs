using Microsoft.EntityFrameworkCore;
using BusSystem.Core.Entities;

namespace BusSystem.Infrastructure.Data;

/// <summary>
/// TimescaleDB数据库上下文（时序数据）
/// </summary>
public class TimescaleDbContext : DbContext
{
    public TimescaleDbContext(DbContextOptions<TimescaleDbContext> options) : base(options)
    {
    }

    /// <summary>
    /// 车辆位置记录（时序表）
    /// </summary>
    public DbSet<VehiclePosition> VehiclePositions { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);
        
        ConfigureVehiclePosition(modelBuilder);
    }

    /// <summary>
    /// 配置车辆位置实体
    /// </summary>
    private static void ConfigureVehiclePosition(ModelBuilder modelBuilder)
    {
        var entity = modelBuilder.Entity<VehiclePosition>();
        
        // 表名
        entity.ToTable("vehicle_positions");
        
        // 复合主键（时间戳 + 车辆ID）
        entity.HasKey(e => new { e.Timestamp, e.VehicleId });
        
        // 属性配置
        entity.Property(e => e.Timestamp)
            .HasColumnName("timestamp")
            .IsRequired();
            
        entity.Property(e => e.VehicleId)
            .HasColumnName("vehicle_id")
            .IsRequired();
            
        entity.Property(e => e.Latitude)
            .HasColumnName("latitude")
            .IsRequired();
            
        entity.Property(e => e.Longitude)
            .HasColumnName("longitude")
            .IsRequired();
            
        entity.Property(e => e.Speed)
            .HasColumnName("speed");
            
        entity.Property(e => e.Direction)
            .HasColumnName("direction");
            
        entity.Property(e => e.Accuracy)
            .HasColumnName("accuracy");
            
        entity.Property(e => e.CurrentStopId)
            .HasColumnName("current_stop_id");
            
        entity.Property(e => e.NextStopId)
            .HasColumnName("next_stop_id");
            
        entity.Property(e => e.Status)
            .HasColumnName("status")
            .HasDefaultValue(1);
        
        // 索引
        entity.HasIndex(e => e.VehicleId)
            .HasDatabaseName("idx_vehicle_positions_vehicle_id");
            
        entity.HasIndex(e => e.Timestamp)
            .HasDatabaseName("idx_vehicle_positions_timestamp");
            
        entity.HasIndex(e => new { e.VehicleId, e.Timestamp })
            .HasDatabaseName("idx_vehicle_positions_vehicle_time");
    }
}

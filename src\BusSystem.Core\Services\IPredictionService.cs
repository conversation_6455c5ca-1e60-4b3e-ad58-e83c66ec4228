namespace BusSystem.Core.Services;

/// <summary>
/// 到站预测服务接口
/// </summary>
public interface IPredictionService
{
    /// <summary>
    /// 预测车辆到达指定站点的时间
    /// </summary>
    Task<object> PredictArrivalTimeAsync(int vehicleId, int stopId);
    
    /// <summary>
    /// 获取站点的所有车辆到站预测
    /// </summary>
    Task<IEnumerable<object>> GetStopArrivalPredictionsAsync(int stopId);
    
    /// <summary>
    /// 获取线路上所有车辆的到站预测
    /// </summary>
    Task<IEnumerable<object>> GetLineArrivalPredictionsAsync(int lineId);
    
    /// <summary>
    /// 计算车辆到下一站的预计时间
    /// </summary>
    Task<object> PredictNextStopArrivalAsync(int vehicleId);
    
    /// <summary>
    /// 基于历史数据预测行程时间
    /// </summary>
    Task<double> PredictTravelTimeAsync(int fromStopId, int toStopId, int lineId, DateTime? departureTime = null);
    
    /// <summary>
    /// 更新预测模型（基于实际到站数据）
    /// </summary>
    Task UpdatePredictionModelAsync(int vehicleId, int stopId, DateTime actualArrivalTime);
    
    /// <summary>
    /// 获取预测准确率统计
    /// </summary>
    Task<object> GetPredictionAccuracyStatsAsync(int lineId, DateTime date);
}

-- 实时公交系统 PostgreSQL 初始化脚本
-- 创建基础数据表和PostGIS扩展

-- 启用PostGIS扩展
CREATE EXTENSION IF NOT EXISTS postgis;
CREATE EXTENSION IF NOT EXISTS postgis_topology;

-- 创建基础数据表

-- 1. 线路表
CREATE TABLE IF NOT EXISTS lines (
    id SERIAL PRIMARY KEY,
    line_id VARCHAR(50) UNIQUE NOT NULL,
    line_number VARCHAR(20) NOT NULL,
    line_name VARCHAR(100) NOT NULL,
    direction INTEGER NOT NULL DEFAULT 0, -- 0: 上行, 1: 下行
    start_stop VARCHAR(100) NOT NULL,
    end_stop VARCHAR(100) NOT NULL,
    operation_start_time TIME NOT NULL DEFAULT '06:00:00',
    operation_end_time TIME NOT NULL DEFAULT '22:00:00',
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 2. 站点表
CREATE TABLE IF NOT EXISTS stops (
    id SERIAL PRIMARY KEY,
    stop_id VARCHAR(50) UNIQUE NOT NULL,
    stop_name VARCHAR(100) NOT NULL,
    stop_desc TEXT,
    location GEOMETRY(POINT, 4326) NOT NULL, -- PostGIS点类型，WGS84坐标系
    address VARCHAR(200),
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 3. 线路站点关联表
CREATE TABLE IF NOT EXISTS line_stops (
    id SERIAL PRIMARY KEY,
    line_id VARCHAR(50) NOT NULL,
    stop_id VARCHAR(50) NOT NULL,
    sequence INTEGER NOT NULL, -- 站点在线路中的顺序
    distance_from_start DECIMAL(10,2), -- 距离起点的距离(米)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (line_id) REFERENCES lines(line_id) ON DELETE CASCADE,
    FOREIGN KEY (stop_id) REFERENCES stops(stop_id) ON DELETE CASCADE,
    UNIQUE(line_id, stop_id),
    UNIQUE(line_id, sequence)
);

-- 4. 车辆表
CREATE TABLE IF NOT EXISTS vehicles (
    id SERIAL PRIMARY KEY,
    vehicle_id VARCHAR(50) UNIQUE NOT NULL,
    line_id VARCHAR(50),
    vehicle_number VARCHAR(20),
    capacity INTEGER DEFAULT 50,
    vehicle_type VARCHAR(20) DEFAULT 'bus',
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (line_id) REFERENCES lines(line_id) ON DELETE SET NULL
);

-- 5. 车辆当前状态表
CREATE TABLE IF NOT EXISTS vehicle_status (
    id SERIAL PRIMARY KEY,
    vehicle_id VARCHAR(50) UNIQUE NOT NULL,
    current_location GEOMETRY(POINT, 4326),
    speed DECIMAL(5,2) DEFAULT 0,
    direction INTEGER DEFAULT 0, -- 方向角度 0-359
    status VARCHAR(20) DEFAULT 'normal', -- normal, delayed, breakdown, out_of_service
    passenger_count INTEGER DEFAULT 0,
    last_stop_id VARCHAR(50),
    next_stop_id VARCHAR(50),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (vehicle_id) REFERENCES vehicles(vehicle_id) ON DELETE CASCADE,
    FOREIGN KEY (last_stop_id) REFERENCES stops(stop_id) ON DELETE SET NULL,
    FOREIGN KEY (next_stop_id) REFERENCES stops(stop_id) ON DELETE SET NULL
);

-- 6. 到站预测表
CREATE TABLE IF NOT EXISTS arrival_predictions (
    id SERIAL PRIMARY KEY,
    line_id VARCHAR(50) NOT NULL,
    stop_id VARCHAR(50) NOT NULL,
    vehicle_id VARCHAR(50) NOT NULL,
    predicted_arrival_time TIMESTAMP WITH TIME ZONE NOT NULL,
    confidence_level DECIMAL(3,2) DEFAULT 0.8, -- 预测置信度 0-1
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (line_id) REFERENCES lines(line_id) ON DELETE CASCADE,
    FOREIGN KEY (stop_id) REFERENCES stops(stop_id) ON DELETE CASCADE,
    FOREIGN KEY (vehicle_id) REFERENCES vehicles(vehicle_id) ON DELETE CASCADE
);

-- 7. 系统配置表
CREATE TABLE IF NOT EXISTS system_configs (
    id SERIAL PRIMARY KEY,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value TEXT NOT NULL,
    config_desc TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 8. 数据同步日志表
CREATE TABLE IF NOT EXISTS sync_logs (
    id SERIAL PRIMARY KEY,
    source_platform VARCHAR(50) NOT NULL,
    sync_type VARCHAR(20) NOT NULL, -- realtime, basic
    data_count INTEGER DEFAULT 0,
    success_count INTEGER DEFAULT 0,
    error_count INTEGER DEFAULT 0,
    error_message TEXT,
    sync_duration_ms INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引

-- 站点地理位置索引
CREATE INDEX IF NOT EXISTS idx_stops_location ON stops USING GIST (location);

-- 车辆状态地理位置索引
CREATE INDEX IF NOT EXISTS idx_vehicle_status_location ON vehicle_status USING GIST (current_location);

-- 线路站点关联索引
CREATE INDEX IF NOT EXISTS idx_line_stops_line_id ON line_stops(line_id);
CREATE INDEX IF NOT EXISTS idx_line_stops_stop_id ON line_stops(stop_id);
CREATE INDEX IF NOT EXISTS idx_line_stops_sequence ON line_stops(line_id, sequence);

-- 车辆状态索引
CREATE INDEX IF NOT EXISTS idx_vehicle_status_vehicle_id ON vehicle_status(vehicle_id);
CREATE INDEX IF NOT EXISTS idx_vehicle_status_updated_at ON vehicle_status(updated_at);

-- 到站预测索引
CREATE INDEX IF NOT EXISTS idx_arrival_predictions_line_stop ON arrival_predictions(line_id, stop_id);
CREATE INDEX IF NOT EXISTS idx_arrival_predictions_vehicle ON arrival_predictions(vehicle_id);
CREATE INDEX IF NOT EXISTS idx_arrival_predictions_time ON arrival_predictions(predicted_arrival_time);

-- 同步日志索引
CREATE INDEX IF NOT EXISTS idx_sync_logs_platform ON sync_logs(source_platform);
CREATE INDEX IF NOT EXISTS idx_sync_logs_created_at ON sync_logs(created_at);

-- 插入初始配置数据
INSERT INTO system_configs (config_key, config_value, config_desc) VALUES
('gps_update_interval', '30', 'GPS数据更新间隔(秒)'),
('prediction_algorithm', 'linear', '到站预测算法类型'),
('max_prediction_time', '3600', '最大预测时间(秒)'),
('nearby_radius', '500', '附近站点查询半径(米)'),
('cache_ttl', '300', '缓存过期时间(秒)')
ON CONFLICT (config_key) DO NOTHING;

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表创建更新时间触发器
CREATE TRIGGER update_lines_updated_at BEFORE UPDATE ON lines
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_stops_updated_at BEFORE UPDATE ON stops
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_vehicles_updated_at BEFORE UPDATE ON vehicles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_vehicle_status_updated_at BEFORE UPDATE ON vehicle_status
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_system_configs_updated_at BEFORE UPDATE ON system_configs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 完成初始化
SELECT 'PostgreSQL database initialized successfully' as status;

using BusSystem.Shared.Enums;

namespace BusSystem.Shared.Models.Common;

/// <summary>
/// 位置响应模型（支持多坐标系）
/// </summary>
public class LocationResponse
{
    /// <summary>
    /// WGS84坐标（GPS原始坐标）
    /// </summary>
    public CoordinatePoint Wgs84 { get; set; } = new();
    
    /// <summary>
    /// GCJ02坐标（火星坐标，高德、腾讯地图）
    /// </summary>
    public CoordinatePoint Gcj02 { get; set; } = new();
    
    /// <summary>
    /// BD09坐标（百度地图）
    /// </summary>
    public CoordinatePoint Bd09 { get; set; } = new();
}

/// <summary>
/// 坐标点
/// </summary>
public class CoordinatePoint
{
    /// <summary>
    /// 经度
    /// </summary>
    public double Longitude { get; set; }
    
    /// <summary>
    /// 纬度
    /// </summary>
    public double Latitude { get; set; }
}

/// <summary>
/// 车辆位置响应（支持多坐标系）
/// </summary>
public class VehiclePositionResponse
{
    public int VehicleId { get; set; }
    public string VehicleNumber { get; set; } = string.Empty;
    public int LineId { get; set; }
    public DateTime Timestamp { get; set; }
    
    /// <summary>
    /// 多坐标系位置信息
    /// </summary>
    public LocationResponse Location { get; set; } = new();
    
    public double? Speed { get; set; }
    public double? Direction { get; set; }
    public int Status { get; set; }
    public int? CurrentStopId { get; set; }
    public int? NextStopId { get; set; }
}

/// <summary>
/// 站点响应（支持多坐标系）
/// </summary>
public class BusStopResponse
{
    public int Id { get; set; }
    public string StopName { get; set; } = string.Empty;
    public string StopCode { get; set; } = string.Empty;
    
    /// <summary>
    /// 多坐标系位置信息
    /// </summary>
    public LocationResponse Location { get; set; } = new();
    
    public string? Address { get; set; }
    public string? District { get; set; }
    public int StopType { get; set; }
    public int Status { get; set; }
    public double? Distance { get; set; } // 距离用户的距离（米）
}

/// <summary>
/// 位置响应扩展方法
/// </summary>
public static class LocationResponseExtensions
{
    /// <summary>
    /// 获取指定坐标系的坐标
    /// </summary>
    public static CoordinatePoint GetCoordinate(this LocationResponse location, CoordinateSystem coordinateSystem)
    {
        return coordinateSystem switch
        {
            CoordinateSystem.WGS84 => location.Wgs84,
            CoordinateSystem.GCJ02 => location.Gcj02,
            CoordinateSystem.BD09 => location.Bd09,
            _ => location.Wgs84
        };
    }
}

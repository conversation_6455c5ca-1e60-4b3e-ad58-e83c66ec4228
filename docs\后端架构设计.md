# 实时公交系统后端架构设计（独立同步服务模式）

## 1. 架构设计原则

### 1.1 核心设计理念
- **完全解耦**: 数据同步服务与主系统完全独立
- **统一接口**: 通过标准化的数据接口规范进行数据传输
- **独立部署**: 每个调度平台的同步服务可以独立开发、部署、维护
- **标准协议**: 使用HTTP API + 消息队列的方式进行数据传输

### 1.2 架构对比

#### 原架构（适配器模式）
```
┌─────────────────────────────────────────────────────────────┐
│                实时公交系统主服务                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │   平台A     │ │   平台B     │ │   平台C     │           │
│  │  Adapter    │ │  Adapter    │ │  Adapter    │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
```

#### 新架构（独立同步服务）
```
┌─────────────────────────────────────────────────────────────┐
│                实时公交系统主服务                           │
│              (只负责业务逻辑处理)                           │
│                                                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                API Gateway Layer                        │ │
│  │               (统一入口、认证、限流)                    │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                │                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              Business Service Layer                     │ │
│  │      (业务逻辑、数据处理、与调度平台无关)               │ │
│  │ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │ │
│  │ │ Line Service│ │ Stop Service│ │Real Service │        │ │
│  │ └─────────────┘ └─────────────┘ └─────────────┘        │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                │                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │               Data Access Layer                         │ │
│  │            (统一数据访问、缓存管理)                     │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                │                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │             Data Sync API Layer                         │ │
│  │          (接收外部同步数据的API接口)                    │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                ↑
                    统一数据接口规范 (HTTP API + MQ)
                                │
┌─────────────────────────────────────────────────────────────┐
│                独立数据同步服务层                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │   平台A     │ │   平台B     │ │   平台C     │           │
│  │ 同步服务    │ │ 同步服务    │ │ 同步服务    │           │
│  │(独立部署)   │ │(独立部署)   │ │(独立部署)   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                External Dispatch Systems                    │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  智能调度   │ │   海信调度  │ │   其他平台  │           │
│  │   平台A     │ │   平台B     │ │   平台C     │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
```

### 1.3 服务职责划分

#### **实时公交系统主服务**
- **API Gateway**: 统一入口、路由分发、限流熔断
- **Business Services**: 业务逻辑处理，与数据源完全无关
  - **Line Service**: 线路信息管理、线路数据查询
  - **Stop Service**: 站点信息管理、站点数据查询
  - **Realtime Service**: 实时数据处理、车辆位置更新
  - **Prediction Service**: 到站时间预测、算法优化
  - **Search Service**: 搜索功能、索引管理
  - **Notification Service**: 实时推送、WebSocket管理
- **Data Access Layer**: 统一数据访问接口，屏蔽底层存储差异
- **Data Sync API Layer**: 接收外部同步数据的标准化API接口

#### **独立数据同步服务**
- **Platform A Sync Service**: 智能调度平台A的数据同步服务（独立部署）
- **Platform B Sync Service**: 海信调度平台B的数据同步服务（独立部署）
- **Platform C Sync Service**: 其他调度平台的数据同步服务（独立部署）

## 2. 统一数据接口规范

### 2.1 数据推送API规范

#### 2.1.1 实时GPS数据推送
```http
POST /api/v1/sync/realtime/gps
Content-Type: application/json
Authorization: Bearer {sync_token}

{
  "source": "platform_a",
  "timestamp": "2025-01-19T12:00:00Z",
  "data": [
    {
      "busId": "bus_001",
      "lineId": "line_64",
      "location": {
        "latitude": 22.547123,
        "longitude": 114.085456,
        "accuracy": 5.2
      },
      "speed": 25.5,
      "direction": 180,
      "timestamp": "2025-01-19T11:59:30Z",
      "status": "normal",
      "passengerCount": 35,
      "nextStopId": "stop_1001"
    }
  ]
}
```

#### 2.1.2 基础数据同步
```http
POST /api/v1/sync/basic/lines
Content-Type: application/json
Authorization: Bearer {sync_token}

{
  "source": "platform_a",
  "timestamp": "2025-01-19T12:00:00Z",
  "operation": "upsert",
  "data": [
    {
      "lineId": "line_64",
      "lineNumber": "64路",
      "lineName": "火车站-机场",
      "direction": 0,
      "startStop": "火车站",
      "endStop": "机场",
      "operationTime": {
        "startTime": "06:00",
        "endTime": "22:00"
      },
      "stops": [
        {
          "stopId": "stop_001",
          "stopName": "火车站",
          "sequence": 1,
          "location": {
            "latitude": 22.547,
            "longitude": 114.085
          }
        }
      ]
    }
  ]
}
```

### 2.2 标准化数据模型
```csharp
// 标准GPS数据模型
public class StandardBusData
{
    public string BusId { get; set; }
    public string LineId { get; set; }
    public Location Location { get; set; }
    public double Speed { get; set; }
    public int Direction { get; set; }
    public DateTime Timestamp { get; set; }
    public BusStatus Status { get; set; }
    public int? PassengerCount { get; set; }
    public string? NextStopId { get; set; }
}

public class Location
{
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public double Accuracy { get; set; }
}

public enum BusStatus
{
    Normal = 1,
    Delayed = 2,
    Breakdown = 3,
    OutOfService = 4
}

// 同步请求模型
public class SyncRequest<T>
{
    public string Source { get; set; }
    public DateTime Timestamp { get; set; }
    public string? Operation { get; set; } // create, update, delete, upsert
    public T Data { get; set; }
}

// 同步响应模型
public class SyncResponse
{
    public bool Success { get; set; }
    public int ProcessedCount { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime Timestamp { get; set; }
}
```

## 3. 主系统实现设计

### 3.1 数据接收API实现
```csharp
[ApiController]
[Route("api/v1/sync")]
public class DataSyncController : ControllerBase
{
    private readonly IDataSyncService _dataSyncService;
    private readonly ILogger<DataSyncController> _logger;

    public DataSyncController(IDataSyncService dataSyncService, ILogger<DataSyncController> logger)
    {
        _dataSyncService = dataSyncService;
        _logger = logger;
    }

    [HttpPost("realtime/gps")]
    public async Task<IActionResult> ReceiveRealtimeGps([FromBody] SyncRequest<StandardBusData[]> request)
    {
        try
        {
            // 验证数据源
            if (!await _dataSyncService.ValidateSourceAsync(request.Source))
            {
                return Unauthorized($"Invalid data source: {request.Source}");
            }

            // 验证数据格式
            if (!_dataSyncService.ValidateData(request.Data))
            {
                return BadRequest("Invalid data format");
            }

            // 处理数据
            var result = await _dataSyncService.ProcessRealtimeGpsAsync(request);

            return Ok(new SyncResponse
            {
                Success = true,
                ProcessedCount = result.ProcessedCount,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process realtime GPS data from {Source}", request.Source);
            return StatusCode(500, new SyncResponse
            {
                Success = false,
                ErrorMessage = "Internal server error",
                Timestamp = DateTime.UtcNow
            });
        }
    }

    [HttpPost("basic/lines")]
    public async Task<IActionResult> ReceiveBasicLines([FromBody] SyncRequest<StandardLineData[]> request)
    {
        try
        {
            var result = await _dataSyncService.ProcessBasicLinesAsync(request);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process basic line data from {Source}", request.Source);
            return StatusCode(500, new SyncResponse
            {
                Success = false,
                ErrorMessage = ex.Message,
                Timestamp = DateTime.UtcNow
            });
        }
    }

    [HttpPost("basic/stops")]
    public async Task<IActionResult> ReceiveBasicStops([FromBody] SyncRequest<StandardStopData[]> request)
    {
        try
        {
            var result = await _dataSyncService.ProcessBasicStopsAsync(request);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process basic stop data from {Source}", request.Source);
            return StatusCode(500, new SyncResponse
            {
                Success = false,
                ErrorMessage = ex.Message,
                Timestamp = DateTime.UtcNow
            });
        }
    }
}
```

### 3.2 数据同步服务实现
```csharp
public interface IDataSyncService
{
    Task<bool> ValidateSourceAsync(string source);
    bool ValidateData<T>(T data);
    Task<SyncResult> ProcessRealtimeGpsAsync(SyncRequest<StandardBusData[]> request);
    Task<SyncResult> ProcessBasicLinesAsync(SyncRequest<StandardLineData[]> request);
    Task<SyncResult> ProcessBasicStopsAsync(SyncRequest<StandardStopData[]> request);
}

public class DataSyncService : IDataSyncService
{
    private readonly IDataAccessService _dataAccess;
    private readonly IRealtimeService _realtimeService;
    private readonly INotificationService _notificationService;
    private readonly ILogger<DataSyncService> _logger;

    public DataSyncService(
        IDataAccessService dataAccess,
        IRealtimeService realtimeService,
        INotificationService notificationService,
        ILogger<DataSyncService> logger)
    {
        _dataAccess = dataAccess;
        _realtimeService = realtimeService;
        _notificationService = notificationService;
        _logger = logger;
    }

    public async Task<bool> ValidateSourceAsync(string source)
    {
        // 验证数据源是否在允许列表中
        var allowedSources = new[] { "platform_a", "hisense", "platform_c" };
        return allowedSources.Contains(source.ToLower());
    }

    public bool ValidateData<T>(T data)
    {
        if (data == null) return false;

        // 根据数据类型进行具体验证
        return data switch
        {
            StandardBusData[] gpsData => ValidateGpsData(gpsData),
            StandardLineData[] lineData => ValidateLineData(lineData),
            StandardStopData[] stopData => ValidateStopData(stopData),
            _ => false
        };
    }

    public async Task<SyncResult> ProcessRealtimeGpsAsync(SyncRequest<StandardBusData[]> request)
    {
        var processedCount = 0;
        var errors = new List<string>();

        foreach (var gpsData in request.Data)
        {
            try
            {
                // 保存GPS数据
                await _dataAccess.SaveBusPositionAsync(gpsData);

                // 触发实时处理
                await _realtimeService.HandleRealtimeUpdateAsync(gpsData);

                processedCount++;
            }
            catch (Exception ex)
            {
                errors.Add($"Failed to process GPS data for bus {gpsData.BusId}: {ex.Message}");
                _logger.LogError(ex, "Failed to process GPS data for bus {BusId}", gpsData.BusId);
            }
        }

        return new SyncResult
        {
            ProcessedCount = processedCount,
            ErrorCount = errors.Count,
            Errors = errors
        };
    }

    private bool ValidateGpsData(StandardBusData[] data)
    {
        return data.All(item =>
            !string.IsNullOrEmpty(item.BusId) &&
            !string.IsNullOrEmpty(item.LineId) &&
            item.Location.Latitude >= -90 && item.Location.Latitude <= 90 &&
            item.Location.Longitude >= -180 && item.Location.Longitude <= 180 &&
            item.Speed >= 0 && item.Speed <= 120);
    }
}
```

## 4. 独立同步服务设计

### 4.1 同步服务基类
```csharp
public abstract class BaseSyncService : BackgroundService
{
    protected readonly ILogger<BaseSyncService> _logger;
    protected readonly HttpClient _httpClient;
    protected readonly IConfiguration _config;
    protected readonly IMessageProducer _messageProducer;

    public abstract string PlatformName { get; }

    protected BaseSyncService(
        ILogger<BaseSyncService> logger,
        HttpClient httpClient,
        IConfiguration config,
        IMessageProducer messageProducer)
    {
        _logger = logger;
        _httpClient = httpClient;
        _config = config;
        _messageProducer = messageProducer;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                // 采集数据
                var rawData = await CollectDataAsync();

                // 转换数据
                var standardData = TransformData(rawData);

                // 推送数据
                await PushDataAsync(standardData);

                // 等待下次同步
                await Task.Delay(GetSyncInterval(), stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Data sync failed for platform {Platform}", PlatformName);
                await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
            }
        }
    }

    protected abstract Task<object> CollectDataAsync();
    protected abstract StandardBusData[] TransformData(object rawData);
    protected abstract TimeSpan GetSyncInterval();

    protected async Task PushDataAsync(StandardBusData[] data)
    {
        // HTTP API推送
        var request = new SyncRequest<StandardBusData[]>
        {
            Source = PlatformName,
            Timestamp = DateTime.UtcNow,
            Data = data
        };

        var busSystemApiUrl = _config["BusSystem:ApiUrl"];
        var syncToken = _config["BusSystem:SyncToken"];

        _httpClient.DefaultRequestHeaders.Authorization =
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", syncToken);

        var response = await _httpClient.PostAsJsonAsync($"{busSystemApiUrl}/api/v1/sync/realtime/gps", request);
        response.EnsureSuccessStatusCode();

        _logger.LogInformation("Successfully pushed {Count} GPS records to main system", data.Length);

        // 消息队列推送（可选）
        if (_messageProducer != null)
        {
            foreach (var item in data)
            {
                await _messageProducer.PublishAsync("realtime.gps.updates", new
                {
                    MessageId = Guid.NewGuid().ToString(),
                    Source = PlatformName,
                    Timestamp = DateTime.UtcNow,
                    EventType = "gps_update",
                    Data = item
                });
            }
        }
    }
}
```

### 4.2 具体平台实现示例
```csharp
public class PlatformASyncService : BaseSyncService
{
    public override string PlatformName => "platform_a";

    public PlatformASyncService(
        ILogger<PlatformASyncService> logger,
        HttpClient httpClient,
        IConfiguration config,
        IMessageProducer messageProducer)
        : base(logger, httpClient, config, messageProducer)
    {
    }

    protected override async Task<object> CollectDataAsync()
    {
        var apiUrl = _config["PlatformA:ApiUrl"];
        var apiKey = _config["PlatformA:ApiKey"];

        _httpClient.DefaultRequestHeaders.Authorization =
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", apiKey);

        var response = await _httpClient.GetAsync($"{apiUrl}/vehicles/realtime");
        response.EnsureSuccessStatusCode();

        var data = await response.Content.ReadFromJsonAsync<PlatformAGpsData[]>();
        _logger.LogInformation("Collected {Count} GPS records from Platform A", data?.Length ?? 0);

        return data ?? Array.Empty<PlatformAGpsData>();
    }

    protected override StandardBusData[] TransformData(object rawData)
    {
        var platformData = (PlatformAGpsData[])rawData;

        return platformData.Select(item => new StandardBusData
        {
            BusId = item.VehicleId,
            LineId = item.RouteId,
            Location = new Location
            {
                Latitude = item.Lat,
                Longitude = item.Lng,
                Accuracy = item.Accuracy ?? 10
            },
            Speed = item.SpeedKmh,
            Direction = item.Heading,
            Timestamp = DateTimeOffset.FromUnixTimeSeconds(item.Timestamp).DateTime,
            Status = MapStatus(item.Status),
            PassengerCount = item.PassengerCount
        }).ToArray();
    }

    protected override TimeSpan GetSyncInterval()
    {
        return TimeSpan.FromSeconds(_config.GetValue<int>("PlatformA:SyncIntervalSeconds", 30));
    }

    private BusStatus MapStatus(string platformStatus)
    {
        return platformStatus?.ToLower() switch
        {
            "normal" => BusStatus.Normal,
            "delayed" => BusStatus.Delayed,
            "breakdown" => BusStatus.Breakdown,
            _ => BusStatus.Normal
        };
    }
}

// 平台A的数据模型
public class PlatformAGpsData
{
    public string VehicleId { get; set; }
    public string RouteId { get; set; }
    public double Lat { get; set; }
    public double Lng { get; set; }
    public double? Accuracy { get; set; }
    public double SpeedKmh { get; set; }
    public int Heading { get; set; }
    public long Timestamp { get; set; }
    public string Status { get; set; }
    public int? PassengerCount { get; set; }
}
```

### 4.3 项目结构
```
BusSystem.DataSync.PlatformA/
├── Services/
│   ├── PlatformASyncService.cs
│   ├── PlatformAApiClient.cs
│   └── DataTransformService.cs
├── Models/
│   ├── PlatformAModels.cs
│   └── StandardModels.cs
├── Configuration/
│   └── PlatformAConfig.cs
├── Program.cs
├── appsettings.json
├── appsettings.Production.json
└── Dockerfile

BusSystem.DataSync.Hisense/
├── Services/
│   ├── HisenseSyncService.cs
│   ├── HisenseSoapClient.cs
│   └── DataTransformService.cs
├── Models/
│   ├── HisenseModels.cs
│   └── StandardModels.cs
└── ...
```

## 5. 部署和运维

### 5.1 Docker部署配置
```yaml
# docker-compose.yml
version: '3.8'
services:
  # 主系统
  bus-system-main:
    image: bus-system/main:latest
    ports:
      - "5000:80"
    environment:
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=BusSystem;Username=postgres;Password=password
      - ConnectionStrings__Redis=redis:6379
      - ConnectionStrings__TimescaleDB=Host=timescaledb;Database=BusSystem;Username=postgres;Password=password
    depends_on:
      - postgres
      - redis
      - timescaledb
    restart: unless-stopped

  # 平台A同步服务
  platform-a-sync:
    image: bus-system/platform-a-sync:latest
    environment:
      - PlatformA__ApiUrl=https://platform-a.com/api
      - PlatformA__ApiKey=${PLATFORM_A_API_KEY}
      - PlatformA__SyncIntervalSeconds=30
      - BusSystem__ApiUrl=http://bus-system-main/api/v1/sync
      - BusSystem__SyncToken=${BUS_SYSTEM_SYNC_TOKEN}
    restart: unless-stopped
    depends_on:
      - bus-system-main

  # 海信同步服务
  hisense-sync:
    image: bus-system/hisense-sync:latest
    environment:
      - Hisense__WsdlUrl=https://hisense.com/service.wsdl
      - Hisense__Username=${HISENSE_USERNAME}
      - Hisense__Password=${HISENSE_PASSWORD}
      - Hisense__SyncIntervalSeconds=60
      - BusSystem__ApiUrl=http://bus-system-main/api/v1/sync
      - BusSystem__SyncToken=${BUS_SYSTEM_SYNC_TOKEN}
    restart: unless-stopped
    depends_on:
      - bus-system-main

  # 数据库服务
  postgres:
    image: postgis/postgis:15-3.3
    environment:
      - POSTGRES_DB=BusSystem
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  timescaledb:
    image: timescale/timescaledb:latest-pg15
    environment:
      - POSTGRES_DB=BusSystem
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - timescaledb_data:/var/lib/postgresql/data
    ports:
      - "5433:5432"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  # 消息队列（可选）
  rabbitmq:
    image: rabbitmq:3-management
    environment:
      - RABBITMQ_DEFAULT_USER=admin
      - RABBITMQ_DEFAULT_PASS=password
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq

volumes:
  postgres_data:
  timescaledb_data:
  redis_data:
  rabbitmq_data:
```

### 5.2 Kubernetes部署配置
```yaml
# k8s/main-system-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: bus-system-main
spec:
  replicas: 3
  selector:
    matchLabels:
      app: bus-system-main
  template:
    metadata:
      labels:
        app: bus-system-main
    spec:
      containers:
      - name: bus-system-main
        image: bus-system/main:latest
        ports:
        - containerPort: 80
        env:
        - name: ConnectionStrings__DefaultConnection
          valueFrom:
            secretKeyRef:
              name: database-secret
              key: postgres-connection
        - name: ConnectionStrings__Redis
          value: "redis-service:6379"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"

---
# k8s/platform-a-sync-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: platform-a-sync
spec:
  replicas: 1
  selector:
    matchLabels:
      app: platform-a-sync
  template:
    metadata:
      labels:
        app: platform-a-sync
    spec:
      containers:
      - name: platform-a-sync
        image: bus-system/platform-a-sync:latest
        env:
        - name: PlatformA__ApiUrl
          value: "https://platform-a.com/api"
        - name: PlatformA__ApiKey
          valueFrom:
            secretKeyRef:
              name: platform-secrets
              key: platform-a-api-key
        - name: BusSystem__ApiUrl
          value: "http://bus-system-main-service/api/v1/sync"
        - name: BusSystem__SyncToken
          valueFrom:
            secretKeyRef:
              name: sync-secrets
              key: sync-token
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"
```

### 5.3 监控和健康检查
```csharp
// 同步服务健康检查
public class SyncServiceHealthCheck : IHealthCheck
{
    private readonly BaseSyncService _syncService;
    private readonly ILogger<SyncServiceHealthCheck> _logger;

    public SyncServiceHealthCheck(BaseSyncService syncService, ILogger<SyncServiceHealthCheck> logger)
    {
        _syncService = syncService;
        _logger = logger;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            // 检查最后同步时间
            var lastSyncTime = await GetLastSyncTimeAsync();
            var timeSinceLastSync = DateTime.UtcNow - lastSyncTime;

            if (timeSinceLastSync > TimeSpan.FromMinutes(5))
            {
                return HealthCheckResult.Unhealthy($"Last sync was {timeSinceLastSync.TotalMinutes:F1} minutes ago");
            }

            // 检查外部API连接
            var apiHealthy = await CheckExternalApiAsync();
            if (!apiHealthy)
            {
                return HealthCheckResult.Degraded("External API connection issues");
            }

            return HealthCheckResult.Healthy($"Sync service is running normally. Last sync: {timeSinceLastSync.TotalMinutes:F1} minutes ago");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Health check failed");
            return HealthCheckResult.Unhealthy("Sync service health check failed", ex);
        }
    }

    private async Task<DateTime> GetLastSyncTimeAsync()
    {
        // 从Redis或数据库获取最后同步时间
        // 实现具体逻辑
        return DateTime.UtcNow.AddMinutes(-2);
    }

    private async Task<bool> CheckExternalApiAsync()
    {
        // 检查外部API是否可访问
        // 实现具体逻辑
        return true;
    }
}

// Program.cs中注册健康检查
builder.Services.AddHealthChecks()
    .AddCheck<SyncServiceHealthCheck>("sync_service")
    .AddCheck("external_api", () => HealthCheckResult.Healthy("External API is accessible"));

app.MapHealthChecks("/health", new HealthCheckOptions
{
    ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
});
```

### 5.4 日志和监控配置
```csharp
// appsettings.json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "BusSystem.DataSync": "Debug"
    }
  },
  "Serilog": {
    "Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"],
    "MinimumLevel": "Information",
    "WriteTo": [
      {
        "Name": "Console",
        "Args": {
          "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"
        }
      },
      {
        "Name": "File",
        "Args": {
          "path": "/app/logs/sync-.log",
          "rollingInterval": "Day",
          "retainedFileCountLimit": 7
        }
      }
    ],
    "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]
  },
  "Metrics": {
    "Prometheus": {
      "Enabled": true,
      "Port": 9090
    }
  }
}
```

## 6. 架构优势总结

### 6.1 技术优势
- **完全解耦**: 主系统与数据同步完全分离，互不影响
- **独立扩展**: 每个同步服务可以独立扩缩容
- **技术灵活**: 同步服务可以使用不同技术栈
- **故障隔离**: 单个平台故障不影响其他服务

### 6.2 运维优势
- **独立部署**: 可以根据需要独立部署和更新
- **版本管理**: 每个服务独立版本控制
- **监控告警**: 针对性监控和告警
- **资源优化**: 根据数据量和频率优化资源配置

### 6.3 业务优势
- **快速接入**: 新增调度平台只需开发独立同步服务
- **平台切换**: 无需修改主系统即可切换数据源
- **多源并行**: 支持多个调度平台同时接入
- **数据标准化**: 统一的数据格式和接口规范

这种架构设计真正实现了"高内聚、低耦合"的微服务理念，为系统的长期发展和维护奠定了坚实的基础。
```
```
                                │
┌─────────────────────────────────────────────────────────────┐
│                External Dispatch Systems                    │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  智能调度   │ │   海信调度  │ │   其他平台  │           │
│  │   平台A     │ │   平台B     │ │   平台C     │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
```

## 2. 数据同步适配器设计

### 2.1 适配器接口标准
```typescript
// 统一的数据同步接口
interface IDataSyncAdapter {
  // 适配器基本信息
  readonly name: string;
  readonly version: string;
  readonly supportedDataTypes: DataType[];
  
  // 连接管理
  connect(config: AdapterConfig): Promise<void>;
  disconnect(): Promise<void>;
  isConnected(): boolean;
  
  // 数据同步方法
  syncBasicData(): Promise<SyncResult>;
  syncRealtimeData(): Promise<SyncResult>;
  
  // 数据订阅（实时推送）
  subscribeRealtimeUpdates(callback: RealtimeCallback): void;
  unsubscribeRealtimeUpdates(): void;
  
  // 健康检查
  healthCheck(): Promise<HealthStatus>;
}

// 标准化数据模型
interface StandardBusData {
  busId: string;
  lineId: string;
  location: {
    latitude: number;
    longitude: number;
    accuracy: number;
  };
  speed: number;
  direction: number;
  timestamp: Date;
  status: BusStatus;
  passengerCount?: number;
}

interface StandardLineData {
  lineId: string;
  lineNumber: string;
  lineName: string;
  direction: number;
  stops: StandardStopData[];
  operationTime: {
    startTime: string;
    endTime: string;
  };
}
```

### 2.2 适配器工厂模式
```typescript
class AdapterFactory {
  private adapters = new Map<string, IDataSyncAdapter>();
  
  // 注册适配器
  registerAdapter(name: string, adapter: IDataSyncAdapter): void {
    this.adapters.set(name, adapter);
  }
  
  // 创建适配器实例
  createAdapter(name: string, config: AdapterConfig): IDataSyncAdapter {
    const AdapterClass = this.adapters.get(name);
    if (!AdapterClass) {
      throw new Error(`Adapter ${name} not found`);
    }
    return new AdapterClass(config);
  }
  
  // 获取所有支持的适配器
  getSupportedAdapters(): string[] {
    return Array.from(this.adapters.keys());
  }
}
```

### 2.3 具体适配器实现示例

#### 2.3.1 智能调度平台A适配器
```typescript
class PlatformAAdapter implements IDataSyncAdapter {
  readonly name = 'PlatformA';
  readonly version = '1.0.0';
  readonly supportedDataTypes = [DataType.GPS, DataType.LINE, DataType.STOP];
  
  private client: PlatformAClient;
  private config: PlatformAConfig;
  
  async connect(config: AdapterConfig): Promise<void> {
    this.config = config as PlatformAConfig;
    this.client = new PlatformAClient({
      apiUrl: this.config.apiUrl,
      apiKey: this.config.apiKey,
      timeout: this.config.timeout || 30000
    });
    
    await this.client.authenticate();
  }
  
  async syncRealtimeData(): Promise<SyncResult> {
    try {
      // 调用平台A的GPS数据接口
      const rawData = await this.client.getRealtimeGPS();
      
      // 转换为标准格式
      const standardData = this.transformGPSData(rawData);
      
      // 存储到数据库
      await this.saveGPSData(standardData);
      
      return {
        success: true,
        recordsProcessed: standardData.length,
        timestamp: new Date()
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date()
      };
    }
  }
  
  private transformGPSData(rawData: any[]): StandardBusData[] {
    return rawData.map(item => ({
      busId: item.vehicle_id,
      lineId: item.route_id,
      location: {
        latitude: item.lat,
        longitude: item.lng,
        accuracy: item.accuracy || 10
      },
      speed: item.speed_kmh,
      direction: item.heading,
      timestamp: new Date(item.timestamp * 1000),
      status: this.mapBusStatus(item.status),
      passengerCount: item.passenger_count
    }));
  }
  
  subscribeRealtimeUpdates(callback: RealtimeCallback): void {
    // 建立WebSocket连接或轮询
    this.client.onRealtimeUpdate((data) => {
      const standardData = this.transformGPSData([data]);
      callback(standardData[0]);
    });
  }
}
```

#### 2.3.2 海信调度平台适配器
```typescript
class HisenseAdapter implements IDataSyncAdapter {
  readonly name = 'Hisense';
  readonly version = '1.0.0';
  readonly supportedDataTypes = [DataType.GPS, DataType.LINE, DataType.STOP];
  
  private soapClient: SOAPClient;
  private config: HisenseConfig;
  
  async connect(config: AdapterConfig): Promise<void> {
    this.config = config as HisenseConfig;
    this.soapClient = new SOAPClient({
      wsdl: this.config.wsdlUrl,
      username: this.config.username,
      password: this.config.password
    });
  }
  
  async syncRealtimeData(): Promise<SyncResult> {
    try {
      // 调用海信平台的SOAP接口
      const response = await this.soapClient.call('GetVehiclePositions', {
        timestamp: new Date().toISOString()
      });
      
      // 解析XML响应并转换为标准格式
      const standardData = this.parseXMLResponse(response);
      
      await this.saveGPSData(standardData);
      
      return {
        success: true,
        recordsProcessed: standardData.length,
        timestamp: new Date()
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date()
      };
    }
  }
  
  private parseXMLResponse(xmlData: string): StandardBusData[] {
    // XML解析逻辑，转换为标准格式
    const parser = new XMLParser();
    const data = parser.parse(xmlData);
    
    return data.vehicles.map(vehicle => ({
      busId: vehicle.vehicleNo,
      lineId: vehicle.lineNo,
      location: {
        latitude: parseFloat(vehicle.latitude),
        longitude: parseFloat(vehicle.longitude),
        accuracy: 15 // 海信平台默认精度
      },
      speed: parseFloat(vehicle.speed),
      direction: parseInt(vehicle.direction),
      timestamp: new Date(vehicle.updateTime),
      status: this.mapHisenseStatus(vehicle.status)
    }));
  }
}
```

## 3. 数据同步服务设计

### 3.1 同步服务管理器
```typescript
class DataSyncManager {
  private adapters: Map<string, IDataSyncAdapter> = new Map();
  private syncScheduler: SyncScheduler;
  private eventBus: EventBus;
  
  constructor() {
    this.syncScheduler = new SyncScheduler();
    this.eventBus = new EventBus();
  }
  
  // 注册适配器
  async registerAdapter(name: string, adapter: IDataSyncAdapter): Promise<void> {
    await adapter.connect();
    this.adapters.set(name, adapter);
    
    // 订阅实时数据
    adapter.subscribeRealtimeUpdates((data) => {
      this.handleRealtimeData(data);
    });
    
    logger.info(`Adapter ${name} registered successfully`);
  }
  
  // 启动同步任务
  startSync(): void {
    // 基础数据同步（每日一次）
    this.syncScheduler.schedule('basic-data', '0 2 * * *', async () => {
      for (const [name, adapter] of this.adapters) {
        try {
          await adapter.syncBasicData();
          logger.info(`Basic data sync completed for ${name}`);
        } catch (error) {
          logger.error(`Basic data sync failed for ${name}:`, error);
        }
      }
    });
    
    // 实时数据同步（每30秒）
    this.syncScheduler.schedule('realtime-data', '*/30 * * * * *', async () => {
      for (const [name, adapter] of this.adapters) {
        try {
          await adapter.syncRealtimeData();
        } catch (error) {
          logger.error(`Realtime data sync failed for ${name}:`, error);
        }
      }
    });
  }
  
  // 处理实时数据
  private async handleRealtimeData(data: StandardBusData): Promise<void> {
    // 数据验证
    if (!this.validateData(data)) {
      logger.warn('Invalid realtime data received:', data);
      return;
    }
    
    // 存储到数据库
    await this.saveToDatabase(data);
    
    // 更新Redis缓存
    await this.updateCache(data);
    
    // 发布事件
    this.eventBus.emit('realtime-data-updated', data);
  }
  
  // 切换适配器
  async switchAdapter(oldName: string, newName: string, config: AdapterConfig): Promise<void> {
    // 停止旧适配器
    const oldAdapter = this.adapters.get(oldName);
    if (oldAdapter) {
      oldAdapter.unsubscribeRealtimeUpdates();
      await oldAdapter.disconnect();
      this.adapters.delete(oldName);
    }
    
    // 启动新适配器
    const newAdapter = AdapterFactory.createAdapter(newName, config);
    await this.registerAdapter(newName, newAdapter);
    
    logger.info(`Switched from ${oldName} to ${newName} adapter`);
  }
}
```

### 3.2 配置管理
```typescript
interface SyncConfig {
  adapters: {
    [name: string]: {
      enabled: boolean;
      config: AdapterConfig;
      priority: number; // 多适配器时的优先级
    };
  };
  syncIntervals: {
    basicData: string; // cron表达式
    realtimeData: string;
  };
  dataValidation: {
    enabled: boolean;
    rules: ValidationRule[];
  };
  fallback: {
    enabled: boolean;
    primaryAdapter: string;
    fallbackAdapter: string;
  };
}

// 配置示例
const syncConfig: SyncConfig = {
  adapters: {
    'PlatformA': {
      enabled: true,
      config: {
        apiUrl: 'https://platform-a.com/api',
        apiKey: 'your-api-key',
        timeout: 30000
      },
      priority: 1
    },
    'Hisense': {
      enabled: false, // 备用适配器
      config: {
        wsdlUrl: 'https://hisense.com/service.wsdl',
        username: 'user',
        password: 'pass'
      },
      priority: 2
    }
  },
  syncIntervals: {
    basicData: '0 2 * * *', // 每天凌晨2点
    realtimeData: '*/30 * * * * *' // 每30秒
  },
  dataValidation: {
    enabled: true,
    rules: [
      { field: 'location.latitude', min: -90, max: 90 },
      { field: 'location.longitude', min: -180, max: 180 },
      { field: 'speed', min: 0, max: 120 }
    ]
  },
  fallback: {
    enabled: true,
    primaryAdapter: 'PlatformA',
    fallbackAdapter: 'Hisense'
  }
};
```

## 4. 业务服务层设计

### 4.1 服务解耦设计
```typescript
// 业务服务只依赖标准化的数据访问层
class LineService {
  constructor(
    private dataAccess: IDataAccess,
    private cacheService: ICacheService
  ) {}
  
  async getLineInfo(lineId: string): Promise<LineInfo> {
    // 业务逻辑与数据源无关
    const cacheKey = `line:${lineId}`;
    let lineInfo = await this.cacheService.get(cacheKey);
    
    if (!lineInfo) {
      lineInfo = await this.dataAccess.getLine(lineId);
      await this.cacheService.set(cacheKey, lineInfo, 3600);
    }
    
    return lineInfo;
  }
  
  async getRealtimeBuses(lineId: string): Promise<RealtimeBus[]> {
    // 从Redis Geo获取实时位置
    const buses = await this.dataAccess.getLineBuses(lineId);
    
    // 获取到站预测
    const predictions = await this.dataAccess.getArrivalPredictions(lineId);
    
    // 组合数据
    return this.combineBusData(buses, predictions);
  }
}
```

### 4.2 数据访问层抽象
```typescript
interface IDataAccess {
  // 基础数据访问
  getLine(lineId: string): Promise<LineInfo>;
  getStop(stopId: string): Promise<StopInfo>;
  getBus(busId: string): Promise<BusInfo>;
  
  // 实时数据访问
  getLineBuses(lineId: string): Promise<RealtimeBus[]>;
  getNearbyStops(lat: number, lng: number, radius: number): Promise<NearbyStop[]>;
  getArrivalPredictions(lineId: string, stopId?: string): Promise<ArrivalPrediction[]>;
  
  // 数据写入
  saveBusPosition(data: StandardBusData): Promise<void>;
  saveArrivalPrediction(prediction: ArrivalPrediction): Promise<void>;
}
```

## 5. 部署和运维

### 5.1 容器化部署
```yaml
# docker-compose.yml
version: '3.8'
services:
  api-gateway:
    image: bus-system/api-gateway:latest
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
  
  business-services:
    image: bus-system/business-services:latest
    environment:
      - DATABASE_URL=postgresql://...
      - REDIS_URL=redis://...
    
  data-sync-service:
    image: bus-system/data-sync:latest
    environment:
      - SYNC_CONFIG_PATH=/config/sync-config.json
    volumes:
      - ./config:/config
    
  # 适配器可以独立部署
  platform-a-adapter:
    image: bus-system/platform-a-adapter:latest
    environment:
      - PLATFORM_A_API_URL=https://...
      - PLATFORM_A_API_KEY=...
```

### 5.2 监控和告警
```typescript
class AdapterMonitor {
  private metrics: Map<string, AdapterMetrics> = new Map();
  
  // 监控适配器健康状态
  async monitorAdapters(): Promise<void> {
    for (const [name, adapter] of this.adapters) {
      const health = await adapter.healthCheck();
      const metrics = this.metrics.get(name) || new AdapterMetrics();
      
      metrics.updateHealth(health);
      
      // 告警检查
      if (!health.isHealthy) {
        await this.sendAlert(`Adapter ${name} is unhealthy: ${health.message}`);
      }
      
      this.metrics.set(name, metrics);
    }
  }
  
  // 性能监控
  trackSyncPerformance(adapterName: string, syncResult: SyncResult): void {
    const metrics = this.metrics.get(adapterName);
    if (metrics) {
      metrics.recordSync(syncResult);
    }
  }
}
```

这个设计的核心优势是：
1. **适配器完全解耦**：更换调度平台只需开发新适配器
2. **业务服务稳定**：业务逻辑与数据源无关
3. **标准化接口**：统一的数据模型和接口规范
4. **灵活配置**：支持多适配器、优先级、降级等
5. **易于扩展**：新增平台只需实现标准接口

您觉得这个架构设计如何？还有哪些方面需要进一步完善？

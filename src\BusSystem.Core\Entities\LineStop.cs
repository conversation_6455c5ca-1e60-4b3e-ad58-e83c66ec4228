namespace BusSystem.Core.Entities;

/// <summary>
/// 线路站点关联实体
/// </summary>
public class LineStop : BaseEntity
{
    /// <summary>
    /// 线路ID
    /// </summary>
    public int LineId { get; set; }
    
    /// <summary>
    /// 站点ID
    /// </summary>
    public int StopId { get; set; }
    
    /// <summary>
    /// 站点在线路中的序号
    /// </summary>
    public int SequenceNumber { get; set; }
    
    /// <summary>
    /// 距离起点的距离（公里）
    /// </summary>
    public decimal? DistanceFromStart { get; set; }
    
    /// <summary>
    /// 预计行驶时间（分钟）
    /// </summary>
    public int? EstimatedTime { get; set; }
    
    /// <summary>
    /// 是否为重点站
    /// </summary>
    public bool IsKeyStop { get; set; } = false;
    
    /// <summary>
    /// 状态：0-停用，1-正常
    /// </summary>
    public int Status { get; set; } = 1;
    
    /// <summary>
    /// 导航属性：线路
    /// </summary>
    public virtual BusLine Line { get; set; } = null!;
    
    /// <summary>
    /// 导航属性：站点
    /// </summary>
    public virtual BusStop Stop { get; set; } = null!;
}

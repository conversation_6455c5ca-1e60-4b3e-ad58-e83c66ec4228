using System.Collections.Concurrent;
using System.Net.WebSockets;
using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using BusSystem.Shared.Models.WebSocket;
using WebSocketMessageType = BusSystem.Shared.Models.WebSocket.WebSocketMessageType;

namespace BusSystem.Core.Services;

/// <summary>
/// 实时推送通知服务实现
/// </summary>
public class NotificationService : INotificationService
{
    private readonly ConcurrentDictionary<string, WebSocketConnection> _connections = new();
    private readonly ConcurrentDictionary<int, HashSet<string>> _lineSubscriptions = new();
    private readonly ConcurrentDictionary<int, HashSet<string>> _stopSubscriptions = new();
    private readonly ILogger<NotificationService> _logger;
    private readonly JsonSerializerOptions _jsonOptions;

    public NotificationService(ILogger<NotificationService> logger)
    {
        _logger = logger;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false
        };
    }

    public async Task<string> AddConnectionAsync(WebSocket webSocket, string? userId = null)
    {
        var connectionId = Guid.NewGuid().ToString();
        var connection = new WebSocketConnection
        {
            ConnectionId = connectionId,
            WebSocket = webSocket,
            UserId = userId,
            ConnectedAt = DateTime.UtcNow,
            LastActivity = DateTime.UtcNow
        };

        _connections.TryAdd(connectionId, connection);

        // 发送连接确认消息
        var ackMessage = new WebSocketMessage
        {
            Type = WebSocketMessageType.ConnectionAck,
            MessageId = Guid.NewGuid().ToString(),
            Timestamp = DateTime.UtcNow,
            Data = new { ConnectionId = connectionId, Message = "连接成功" }
        };

        await SendMessageToConnectionAsync(connectionId, ackMessage);

        _logger.LogInformation("WebSocket连接已建立，连接ID: {ConnectionId}, 用户ID: {UserId}", 
            connectionId, userId);

        return connectionId;
    }

    public async Task RemoveConnectionAsync(string connectionId)
    {
        if (_connections.TryRemove(connectionId, out var connection))
        {
            // 清理订阅
            await CleanupConnectionSubscriptionsAsync(connectionId);

            // 关闭WebSocket连接
            if (connection.WebSocket.State == WebSocketState.Open)
            {
                try
                {
                    await connection.WebSocket.CloseAsync(
                        WebSocketCloseStatus.NormalClosure, 
                        "连接关闭", 
                        CancellationToken.None);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "关闭WebSocket连接时出错，连接ID: {ConnectionId}", connectionId);
                }
            }

            _logger.LogInformation("WebSocket连接已移除，连接ID: {ConnectionId}", connectionId);
        }
    }

    public async Task SubscribeToLineAsync(string connectionId, int lineId)
    {
        if (_connections.TryGetValue(connectionId, out var connection))
        {
            connection.SubscribedLines.Add(lineId);
            connection.LastActivity = DateTime.UtcNow;

            _lineSubscriptions.AddOrUpdate(lineId,
                new HashSet<string> { connectionId },
                (key, existing) =>
                {
                    existing.Add(connectionId);
                    return existing;
                });

            _logger.LogDebug("连接 {ConnectionId} 订阅了线路 {LineId}", connectionId, lineId);
        }

        await Task.CompletedTask;
    }

    public async Task UnsubscribeFromLineAsync(string connectionId, int lineId)
    {
        if (_connections.TryGetValue(connectionId, out var connection))
        {
            connection.SubscribedLines.Remove(lineId);
            connection.LastActivity = DateTime.UtcNow;

            if (_lineSubscriptions.TryGetValue(lineId, out var subscribers))
            {
                subscribers.Remove(connectionId);
                if (subscribers.Count == 0)
                {
                    _lineSubscriptions.TryRemove(lineId, out _);
                }
            }

            _logger.LogDebug("连接 {ConnectionId} 取消订阅线路 {LineId}", connectionId, lineId);
        }

        await Task.CompletedTask;
    }

    public async Task SubscribeToStopAsync(string connectionId, int stopId)
    {
        if (_connections.TryGetValue(connectionId, out var connection))
        {
            connection.SubscribedStops.Add(stopId);
            connection.LastActivity = DateTime.UtcNow;

            _stopSubscriptions.AddOrUpdate(stopId,
                new HashSet<string> { connectionId },
                (key, existing) =>
                {
                    existing.Add(connectionId);
                    return existing;
                });

            _logger.LogDebug("连接 {ConnectionId} 订阅了站点 {StopId}", connectionId, stopId);
        }

        await Task.CompletedTask;
    }

    public async Task UnsubscribeFromStopAsync(string connectionId, int stopId)
    {
        if (_connections.TryGetValue(connectionId, out var connection))
        {
            connection.SubscribedStops.Remove(stopId);
            connection.LastActivity = DateTime.UtcNow;

            if (_stopSubscriptions.TryGetValue(stopId, out var subscribers))
            {
                subscribers.Remove(connectionId);
                if (subscribers.Count == 0)
                {
                    _stopSubscriptions.TryRemove(stopId, out _);
                }
            }

            _logger.LogDebug("连接 {ConnectionId} 取消订阅站点 {StopId}", connectionId, stopId);
        }

        await Task.CompletedTask;
    }

    public async Task SubscribeToAreaAsync(string connectionId, double longitude, double latitude, double radiusMeters)
    {
        if (_connections.TryGetValue(connectionId, out var connection))
        {
            var areaSubscription = new AreaSubscription
            {
                Longitude = longitude,
                Latitude = latitude,
                RadiusMeters = radiusMeters,
                SubscribedAt = DateTime.UtcNow
            };

            connection.SubscribedAreas.Add(areaSubscription);
            connection.LastActivity = DateTime.UtcNow;

            _logger.LogDebug("连接 {ConnectionId} 订阅了区域 ({Longitude}, {Latitude}), 半径: {Radius}米", 
                connectionId, longitude, latitude, radiusMeters);
        }

        await Task.CompletedTask;
    }

    public async Task NotifyVehiclePositionUpdateAsync(int vehicleId, object positionData)
    {
        var message = new WebSocketMessage
        {
            Type = WebSocketMessageType.VehiclePositionUpdate,
            MessageId = Guid.NewGuid().ToString(),
            Timestamp = DateTime.UtcNow,
            Data = new { VehicleId = vehicleId, Position = positionData }
        };

        // 找到订阅了相关线路的连接
        var targetConnections = new HashSet<string>();

        // 这里需要根据vehicleId找到对应的lineId
        // 简化实现：假设可以从positionData中获取lineId
        if (positionData is IDictionary<string, object> dict && dict.TryGetValue("LineId", out var lineIdObj))
        {
            if (int.TryParse(lineIdObj?.ToString(), out var lineId))
            {
                if (_lineSubscriptions.TryGetValue(lineId, out var lineSubscribers))
                {
                    foreach (var connectionId in lineSubscribers)
                    {
                        targetConnections.Add(connectionId);
                    }
                }
            }
        }

        // 发送到目标连接
        await SendMessageToConnectionsAsync(targetConnections, message);
    }

    public async Task NotifyArrivalPredictionUpdateAsync(int stopId, object predictionData)
    {
        var message = new WebSocketMessage
        {
            Type = WebSocketMessageType.ArrivalPredictionUpdate,
            MessageId = Guid.NewGuid().ToString(),
            Timestamp = DateTime.UtcNow,
            Data = new { StopId = stopId, Predictions = predictionData }
        };

        // 找到订阅了该站点的连接
        var targetConnections = new HashSet<string>();

        if (_stopSubscriptions.TryGetValue(stopId, out var stopSubscribers))
        {
            foreach (var connectionId in stopSubscribers)
            {
                targetConnections.Add(connectionId);
            }
        }

        await SendMessageToConnectionsAsync(targetConnections, message);
    }

    public async Task NotifyLineStatusUpdateAsync(int lineId, object statusData)
    {
        var message = new WebSocketMessage
        {
            Type = WebSocketMessageType.LineStatusUpdate,
            MessageId = Guid.NewGuid().ToString(),
            Timestamp = DateTime.UtcNow,
            Data = new { LineId = lineId, Status = statusData }
        };

        // 找到订阅了该线路的连接
        var targetConnections = new HashSet<string>();

        if (_lineSubscriptions.TryGetValue(lineId, out var lineSubscribers))
        {
            foreach (var connectionId in lineSubscribers)
            {
                targetConnections.Add(connectionId);
            }
        }

        await SendMessageToConnectionsAsync(targetConnections, message);
    }

    public async Task SendMessageToConnectionAsync(string connectionId, object message)
    {
        if (_connections.TryGetValue(connectionId, out var connection))
        {
            await SendMessageAsync(connection, message);
        }
    }

    public async Task BroadcastMessageAsync(object message)
    {
        var tasks = _connections.Values.Select(connection => SendMessageAsync(connection, message));
        await Task.WhenAll(tasks);
    }

    public async Task<object> GetConnectionStatsAsync()
    {
        var now = DateTime.UtcNow;
        var activeConnections = _connections.Values.Where(c => 
            c.WebSocket.State == WebSocketState.Open && 
            (now - c.LastActivity).TotalMinutes < 5).ToList();

        return new
        {
            TotalConnections = _connections.Count,
            ActiveConnections = activeConnections.Count,
            LineSubscriptions = _lineSubscriptions.Count,
            StopSubscriptions = _stopSubscriptions.Count,
            ConnectionDetails = activeConnections.Select(c => new
            {
                c.ConnectionId,
                c.UserId,
                c.ConnectedAt,
                c.LastActivity,
                SubscribedLinesCount = c.SubscribedLines.Count,
                SubscribedStopsCount = c.SubscribedStops.Count,
                SubscribedAreasCount = c.SubscribedAreas.Count
            })
        };
    }

    public async Task CleanupInvalidConnectionsAsync()
    {
        var invalidConnections = _connections.Values
            .Where(c => c.WebSocket.State != WebSocketState.Open || 
                       (DateTime.UtcNow - c.LastActivity).TotalMinutes > 10)
            .ToList();

        foreach (var connection in invalidConnections)
        {
            await RemoveConnectionAsync(connection.ConnectionId);
        }

        _logger.LogInformation("清理了 {Count} 个无效连接", invalidConnections.Count);
    }

    #region 私有方法

    private async Task SendMessageAsync(WebSocketConnection connection, object message)
    {
        if (connection.WebSocket.State != WebSocketState.Open)
        {
            return;
        }

        try
        {
            var json = JsonSerializer.Serialize(message, _jsonOptions);
            var bytes = Encoding.UTF8.GetBytes(json);
            var buffer = new ArraySegment<byte>(bytes);

            await connection.WebSocket.SendAsync(buffer, System.Net.WebSockets.WebSocketMessageType.Text, true, CancellationToken.None);
            connection.LastActivity = DateTime.UtcNow;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "发送WebSocket消息失败，连接ID: {ConnectionId}", connection.ConnectionId);
            await RemoveConnectionAsync(connection.ConnectionId);
        }
    }

    private async Task SendMessageToConnectionsAsync(HashSet<string> connectionIds, object message)
    {
        var tasks = connectionIds
            .Where(id => _connections.ContainsKey(id))
            .Select(id => SendMessageToConnectionAsync(id, message));

        await Task.WhenAll(tasks);
    }

    private async Task CleanupConnectionSubscriptionsAsync(string connectionId)
    {
        // 清理线路订阅
        foreach (var lineId in _lineSubscriptions.Keys.ToList())
        {
            if (_lineSubscriptions.TryGetValue(lineId, out var subscribers))
            {
                subscribers.Remove(connectionId);
                if (subscribers.Count == 0)
                {
                    _lineSubscriptions.TryRemove(lineId, out _);
                }
            }
        }

        // 清理站点订阅
        foreach (var stopId in _stopSubscriptions.Keys.ToList())
        {
            if (_stopSubscriptions.TryGetValue(stopId, out var subscribers))
            {
                subscribers.Remove(connectionId);
                if (subscribers.Count == 0)
                {
                    _stopSubscriptions.TryRemove(stopId, out _);
                }
            }
        }

        await Task.CompletedTask;
    }

    #endregion
}

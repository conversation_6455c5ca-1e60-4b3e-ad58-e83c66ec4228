using System.Net.WebSockets;
using System.Text;
using System.Text.Json;
using BusSystem.Core.Services;
using BusSystem.Shared.Models.WebSocket;
using WebSocketMessageType = BusSystem.Shared.Models.WebSocket.WebSocketMessageType;

namespace BusSystem.Api.Middleware;

/// <summary>
/// WebSocket中间件
/// </summary>
public class WebSocketMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<WebSocketMiddleware> _logger;
    private readonly JsonSerializerOptions _jsonOptions;

    public WebSocketMiddleware(RequestDelegate next, ILogger<WebSocketMiddleware> logger)
    {
        _next = next;
        _logger = logger;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false
        };
    }

    public async Task InvokeAsync(HttpContext context, INotificationService notificationService)
    {
        if (context.Request.Path == "/ws" && context.WebSockets.IsWebSocketRequest)
        {
            var webSocket = await context.WebSockets.AcceptWebSocketAsync();
            var userId = context.Request.Query["userId"].FirstOrDefault();
            
            await HandleWebSocketAsync(webSocket, notificationService, userId);
        }
        else
        {
            await _next(context);
        }
    }

    private async Task HandleWebSocketAsync(WebSocket webSocket, INotificationService notificationService, string? userId)
    {
        var connectionId = await notificationService.AddConnectionAsync(webSocket, userId);
        
        try
        {
            var buffer = new byte[1024 * 4];
            
            while (webSocket.State == WebSocketState.Open)
            {
                var result = await webSocket.ReceiveAsync(new ArraySegment<byte>(buffer), CancellationToken.None);
                
                if (result.MessageType == System.Net.WebSockets.WebSocketMessageType.Text)
                {
                    var message = Encoding.UTF8.GetString(buffer, 0, result.Count);
                    await ProcessWebSocketMessage(connectionId, message, notificationService);
                }
                else if (result.MessageType == System.Net.WebSockets.WebSocketMessageType.Close)
                {
                    await webSocket.CloseAsync(WebSocketCloseStatus.NormalClosure, "连接关闭", CancellationToken.None);
                    break;
                }
            }
        }
        catch (WebSocketException ex)
        {
            _logger.LogWarning(ex, "WebSocket连接异常，连接ID: {ConnectionId}", connectionId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理WebSocket消息时出错，连接ID: {ConnectionId}", connectionId);
        }
        finally
        {
            await notificationService.RemoveConnectionAsync(connectionId);
        }
    }

    private async Task ProcessWebSocketMessage(string connectionId, string message, INotificationService notificationService)
    {
        try
        {
            var wsMessage = JsonSerializer.Deserialize<WebSocketMessage>(message, _jsonOptions);
            if (wsMessage == null) return;

            switch (wsMessage.Type)
            {
                case WebSocketMessageType.Subscribe:
                    await HandleSubscriptionMessage(connectionId, wsMessage, notificationService);
                    break;
                    
                case WebSocketMessageType.Unsubscribe:
                    await HandleUnsubscriptionMessage(connectionId, wsMessage, notificationService);
                    break;
                    
                case WebSocketMessageType.Heartbeat:
                    await HandleHeartbeatMessage(connectionId, notificationService);
                    break;
                    
                default:
                    _logger.LogWarning("未知的WebSocket消息类型: {MessageType}", wsMessage.Type);
                    break;
            }
        }
        catch (JsonException ex)
        {
            _logger.LogWarning(ex, "解析WebSocket消息失败，连接ID: {ConnectionId}, 消息: {Message}", connectionId, message);
            
            var errorMessage = new WebSocketMessage
            {
                Type = WebSocketMessageType.Error,
                MessageId = Guid.NewGuid().ToString(),
                Timestamp = DateTime.UtcNow,
                Error = "消息格式错误"
            };
            
            await notificationService.SendMessageToConnectionAsync(connectionId, errorMessage);
        }
    }

    private async Task HandleSubscriptionMessage(string connectionId, WebSocketMessage wsMessage, INotificationService notificationService)
    {
        try
        {
            if (wsMessage.Data == null) return;
            
            var subscriptionData = JsonSerializer.Deserialize<SubscriptionRequest>(
                wsMessage.Data.ToString() ?? "", _jsonOptions);
            
            if (subscriptionData == null) return;

            switch (subscriptionData.Target.ToLower())
            {
                case "line":
                    if (subscriptionData.LineId.HasValue)
                    {
                        await notificationService.SubscribeToLineAsync(connectionId, subscriptionData.LineId.Value);
                        await SendSubscriptionConfirmation(connectionId, "line", subscriptionData.LineId.Value, notificationService);
                    }
                    break;
                    
                case "stop":
                    if (subscriptionData.StopId.HasValue)
                    {
                        await notificationService.SubscribeToStopAsync(connectionId, subscriptionData.StopId.Value);
                        await SendSubscriptionConfirmation(connectionId, "stop", subscriptionData.StopId.Value, notificationService);
                    }
                    break;
                    
                case "area":
                    if (subscriptionData.Longitude.HasValue && 
                        subscriptionData.Latitude.HasValue && 
                        subscriptionData.RadiusMeters.HasValue)
                    {
                        await notificationService.SubscribeToAreaAsync(
                            connectionId, 
                            subscriptionData.Longitude.Value, 
                            subscriptionData.Latitude.Value, 
                            subscriptionData.RadiusMeters.Value);
                        
                        await SendAreaSubscriptionConfirmation(connectionId, subscriptionData, notificationService);
                    }
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理订阅消息失败，连接ID: {ConnectionId}", connectionId);
        }
    }

    private async Task HandleUnsubscriptionMessage(string connectionId, WebSocketMessage wsMessage, INotificationService notificationService)
    {
        try
        {
            if (wsMessage.Data == null) return;
            
            var subscriptionData = JsonSerializer.Deserialize<SubscriptionRequest>(
                wsMessage.Data.ToString() ?? "", _jsonOptions);
            
            if (subscriptionData == null) return;

            switch (subscriptionData.Target.ToLower())
            {
                case "line":
                    if (subscriptionData.LineId.HasValue)
                    {
                        await notificationService.UnsubscribeFromLineAsync(connectionId, subscriptionData.LineId.Value);
                    }
                    break;
                    
                case "stop":
                    if (subscriptionData.StopId.HasValue)
                    {
                        await notificationService.UnsubscribeFromStopAsync(connectionId, subscriptionData.StopId.Value);
                    }
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理取消订阅消息失败，连接ID: {ConnectionId}", connectionId);
        }
    }

    private async Task HandleHeartbeatMessage(string connectionId, INotificationService notificationService)
    {
        var heartbeatResponse = new WebSocketMessage
        {
            Type = WebSocketMessageType.Heartbeat,
            MessageId = Guid.NewGuid().ToString(),
            Timestamp = DateTime.UtcNow,
            Data = new { Status = "alive" }
        };
        
        await notificationService.SendMessageToConnectionAsync(connectionId, heartbeatResponse);
    }

    private async Task SendSubscriptionConfirmation(string connectionId, string target, int targetId, INotificationService notificationService)
    {
        var confirmationMessage = new WebSocketMessage
        {
            Type = WebSocketMessageType.Subscribe,
            MessageId = Guid.NewGuid().ToString(),
            Timestamp = DateTime.UtcNow,
            Data = new 
            { 
                Status = "confirmed", 
                Target = target, 
                TargetId = targetId,
                Message = $"已订阅{target} {targetId}"
            }
        };
        
        await notificationService.SendMessageToConnectionAsync(connectionId, confirmationMessage);
    }

    private async Task SendAreaSubscriptionConfirmation(string connectionId, SubscriptionRequest request, INotificationService notificationService)
    {
        var confirmationMessage = new WebSocketMessage
        {
            Type = WebSocketMessageType.Subscribe,
            MessageId = Guid.NewGuid().ToString(),
            Timestamp = DateTime.UtcNow,
            Data = new 
            { 
                Status = "confirmed", 
                Target = "area",
                Longitude = request.Longitude,
                Latitude = request.Latitude,
                RadiusMeters = request.RadiusMeters,
                Message = $"已订阅区域 ({request.Longitude}, {request.Latitude}), 半径: {request.RadiusMeters}米"
            }
        };
        
        await notificationService.SendMessageToConnectionAsync(connectionId, confirmationMessage);
    }
}

version: '3.8'
services:
  # PostgreSQL + PostGIS (基础数据)
  postgres:
    image: postgis/postgis:15-3.3
    container_name: bus-postgres
    environment:
      - POSTGRES_DB=BusSystem
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=dev123456
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - bus-network

  # TimescaleDB (时序数据)
  timescaledb:
    image: timescale/timescaledb:latest-pg15
    container_name: bus-timescaledb
    environment:
      - POSTGRES_DB=BusSystem
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=dev123456
    ports:
      - "5433:5432"
    volumes:
      - timescaledb_data:/var/lib/postgresql/data
      - ./init-timescaledb.sql:/docker-entrypoint-initdb.d/init-timescaledb.sql
    networks:
      - bus-network

  # Redis (缓存和地理查询)
  redis:
    image: redis:7-alpine
    container_name: bus-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - bus-network

  # Redis Commander (Redis管理界面，可选)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: bus-redis-commander
    environment:
      - REDIS_HOSTS=local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis
    networks:
      - bus-network

  # pgAdmin (PostgreSQL管理界面，可选)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: bus-pgadmin
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin123
    ports:
      - "8080:80"
    depends_on:
      - postgres
      - timescaledb
    networks:
      - bus-network

volumes:
  postgres_data:
    driver: local
  timescaledb_data:
    driver: local
  redis_data:
    driver: local

networks:
  bus-network:
    driver: bridge

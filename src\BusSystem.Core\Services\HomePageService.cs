using Microsoft.Extensions.Logging;
using BusSystem.Core.Interfaces;

namespace BusSystem.Core.Services;

/// <summary>
/// 首页服务实现
/// </summary>
public class HomePageService : IHomePageService
{
    private readonly IBusStopRepository _stopRepository;
    private readonly IBusLineRepository _lineRepository;
    private readonly IRealtimeService _realtimeService;
    private readonly IPredictionService _predictionService;
    private readonly ICoordinateTransformService _coordinateTransformService;
    private readonly ILogger<HomePageService> _logger;

    public HomePageService(
        IBusStopRepository stopRepository,
        IBusLineRepository lineRepository,
        IRealtimeService realtimeService,
        IPredictionService predictionService,
        ICoordinateTransformService coordinateTransformService,
        ILogger<HomePageService> logger)
    {
        _stopRepository = stopRepository;
        _lineRepository = lineRepository;
        _realtimeService = realtimeService;
        _predictionService = predictionService;
        _coordinateTransformService = coordinateTransformService;
        _logger = logger;
    }

    public async Task<object> GetNearbyStopsWithRealtimeInfoAsync(double longitude, double latitude, double radiusMeters = 500)
    {
        try
        {
            // 1. 获取附近站点
            var nearbyStops = await _stopRepository.GetNearbyStopsAsync(longitude, latitude, radiusMeters);
            
            var result = new List<object>();

            foreach (var stop in nearbyStops)
            {
                // 2. 获取站点的线路信息
                var stopWithLines = await _stopRepository.GetWithLinesAsync(stop.Id);
                if (stopWithLines?.LineStops == null) continue;

                var lines = new List<object>();

                // 3. 获取每条线路的到站预测
                foreach (var lineStop in stopWithLines.LineStops.Where(ls => ls.Line?.Status == 1))
                {
                    var line = lineStop.Line;
                    if (line == null) continue;

                    // 获取该线路的实时车辆位置
                    var linePositions = await _realtimeService.GetLineRealtimePositionsAsync(line.Id);
                    
                    var predictions = new List<object>();
                    
                    // 为每辆车计算到站预测
                    foreach (var position in linePositions.Take(3)) // 只取最近的3辆车
                    {
                        try
                        {
                            var prediction = await _predictionService.PredictArrivalTimeAsync(position.VehicleId, stop.Id);
                            predictions.Add(prediction);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "预测车辆 {VehicleId} 到站点 {StopId} 失败", position.VehicleId, stop.Id);
                        }
                    }

                    lines.Add(new
                    {
                        LineId = line.Id,
                        LineNumber = line.LineNumber,
                        LineName = line.LineName,
                        Direction = line.Direction,
                        StartStopName = line.StartStopName,
                        EndStopName = line.EndStopName,
                        Status = line.Status,
                        Predictions = predictions.OrderBy(p => 
                        {
                            var dict = p as dynamic;
                            return dict?.EstimatedMinutes ?? double.MaxValue;
                        }).Take(2) // 只显示最近的2班车
                    });
                }

                // 计算距离用户的距离
                var distance = CalculateDistance(latitude, longitude, stop.Latitude, stop.Longitude);

                result.Add(new
                {
                    StopId = stop.Id,
                    StopName = stop.StopName,
                    StopCode = stop.StopCode,
                    Location = new
                    {
                        Longitude = stop.Longitude,
                        Latitude = stop.Latitude
                    },
                    Distance = Math.Round(distance, 0), // 距离（米）
                    Address = stop.Address,
                    District = stop.District,
                    StopType = stop.StopType,
                    Lines = lines.OrderBy(l => ((dynamic)l).LineNumber)
                });
            }

            return new
            {
                UserLocation = new { Longitude = longitude, Latitude = latitude },
                SearchRadius = radiusMeters,
                TotalStops = result.Count,
                Stops = result.OrderBy(s => ((dynamic)s).Distance).Take(10) // 按距离排序，最多返回10个站点
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取附近站点实时信息失败，位置: ({Longitude}, {Latitude})", longitude, latitude);
            throw;
        }
    }

    public async Task<object> GetStopDetailWithPredictionsAsync(int stopId)
    {
        try
        {
            var stop = await _stopRepository.GetWithLinesAsync(stopId);
            if (stop == null)
            {
                throw new InvalidOperationException($"站点不存在，ID: {stopId}");
            }

            var lines = new List<object>();

            if (stop.LineStops != null)
            {
                foreach (var lineStop in stop.LineStops.Where(ls => ls.Line?.Status == 1))
                {
                    var line = lineStop.Line;
                    if (line == null) continue;

                    // 获取该线路所有车辆的到站预测
                    var predictions = await _predictionService.GetStopArrivalPredictionsAsync(stopId);
                    var linePredictions = predictions.Where(p =>
                    {
                        var dict = p as dynamic;
                        return dict?.LineId == line.Id;
                    });

                    lines.Add(new
                    {
                        LineId = line.Id,
                        LineNumber = line.LineNumber,
                        LineName = line.LineName,
                        Direction = line.Direction,
                        StartStopName = line.StartStopName,
                        EndStopName = line.EndStopName,
                        SequenceNumber = lineStop.SequenceNumber,
                        DistanceFromStart = lineStop.DistanceFromStart,
                        EstimatedTime = lineStop.EstimatedTime,
                        Predictions = linePredictions.OrderBy(p =>
                        {
                            var dict = p as dynamic;
                            return dict?.EstimatedMinutes ?? double.MaxValue;
                        }).Take(5) // 显示最近5班车
                    });
                }
            }

            return new
            {
                StopId = stop.Id,
                StopName = stop.StopName,
                StopCode = stop.StopCode,
                Location = new
                {
                    Longitude = stop.Longitude,
                    Latitude = stop.Latitude
                },
                Address = stop.Address,
                District = stop.District,
                StopType = stop.StopType,
                Facilities = stop.Facilities,
                TotalLines = lines.Count,
                Lines = lines.OrderBy(l => ((dynamic)l).LineNumber)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取站点详细信息失败，站点ID: {StopId}", stopId);
            throw;
        }
    }

    public async Task<object> SearchStopsAndLinesAsync(string keyword, double? longitude = null, double? latitude = null)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(keyword))
            {
                return new { Stops = new List<object>(), Lines = new List<object>() };
            }

            // 搜索站点
            var stops = await _stopRepository.SearchByNameAsync(keyword.Trim());
            var stopResults = stops.Take(10).Select(stop => new
            {
                StopId = stop.Id,
                StopName = stop.StopName,
                StopCode = stop.StopCode,
                Address = stop.Address,
                District = stop.District,
                Location = new
                {
                    Longitude = stop.Longitude,
                    Latitude = stop.Latitude
                },
                Distance = longitude.HasValue && latitude.HasValue 
                    ? Math.Round(CalculateDistance(latitude.Value, longitude.Value, stop.Latitude, stop.Longitude), 0)
                    : (double?)null
            });

            // 搜索线路
            var lines = await _lineRepository.SearchAsync(keyword.Trim());
            var lineResults = lines.Take(10).Select(line => new
            {
                LineId = line.Id,
                LineNumber = line.LineNumber,
                LineName = line.LineName,
                StartStopName = line.StartStopName,
                EndStopName = line.EndStopName,
                Direction = line.Direction,
                Status = line.Status
            });

            return new
            {
                Keyword = keyword,
                UserLocation = longitude.HasValue && latitude.HasValue 
                    ? new { Longitude = longitude.Value, Latitude = latitude.Value }
                    : null,
                Stops = stopResults.OrderBy(s => s.Distance ?? double.MaxValue),
                Lines = lineResults.OrderBy(l => l.LineNumber)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "搜索站点和线路失败，关键词: {Keyword}", keyword);
            throw;
        }
    }

    #region 私有方法

    private static double CalculateDistance(double lat1, double lon1, double lat2, double lon2)
    {
        const double R = 6371000; // 地球半径（米）
        var dLat = (lat2 - lat1) * Math.PI / 180;
        var dLon = (lon2 - lon1) * Math.PI / 180;
        var a = Math.Sin(dLat / 2) * Math.Sin(dLat / 2) +
                Math.Cos(lat1 * Math.PI / 180) * Math.Cos(lat2 * Math.PI / 180) *
                Math.Sin(dLon / 2) * Math.Sin(dLon / 2);
        var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
        return R * c;
    }

    #endregion
}

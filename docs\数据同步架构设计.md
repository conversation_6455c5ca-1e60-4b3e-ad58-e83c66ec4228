# 实时公交系统数据同步架构设计

## 1. 新架构设计理念

### 1.1 核心思想
- **完全解耦**: 数据同步服务与主系统完全独立
- **统一接口**: 通过标准化的数据接口规范进行数据传输
- **独立部署**: 每个调度平台的同步服务可以独立开发、部署、维护
- **标准协议**: 使用HTTP API + 消息队列的方式进行数据传输

### 1.2 架构对比

#### 原架构（适配器模式）
```
┌─────────────────────────────────────────────────────────────┐
│                实时公交系统主服务                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │   平台A     │ │   平台B     │ │   平台C     │           │
│  │  Adapter    │ │  Adapter    │ │  Adapter    │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                外部调度系统                                 │
└─────────────────────────────────────────────────────────────┘
```

#### 新架构（独立同步服务）
```
┌─────────────────────────────────────────────────────────────┐
│                实时公交系统主服务                           │
│              (只负责业务逻辑处理)                           │
└─────────────────────────────────────────────────────────────┘
                                ↑
                    统一数据接口规范 (HTTP API + MQ)
                                │
┌─────────────────────────────────────────────────────────────┐
│                数据同步服务层                               │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │   平台A     │ │   平台B     │ │   平台C     │           │
│  │ 同步服务    │ │ 同步服务    │ │ 同步服务    │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                外部调度系统                                 │
└─────────────────────────────────────────────────────────────┘
```

## 2. 统一数据接口规范

### 2.1 数据推送API规范

#### 2.1.1 实时GPS数据推送
```http
POST /api/v1/sync/realtime/gps
Content-Type: application/json
Authorization: Bearer {sync_token}

{
  "source": "platform_a",
  "timestamp": "2025-01-19T12:00:00Z",
  "data": [
    {
      "busId": "bus_001",
      "lineId": "line_64",
      "location": {
        "latitude": 22.547123,
        "longitude": 114.085456,
        "accuracy": 5.2
      },
      "speed": 25.5,
      "direction": 180,
      "timestamp": "2025-01-19T11:59:30Z",
      "status": "normal",
      "passengerCount": 35,
      "nextStopId": "stop_1001"
    }
  ]
}
```

#### 2.1.2 基础数据同步
```http
POST /api/v1/sync/basic/lines
Content-Type: application/json
Authorization: Bearer {sync_token}

{
  "source": "platform_a",
  "timestamp": "2025-01-19T12:00:00Z",
  "operation": "upsert", // create, update, delete, upsert
  "data": [
    {
      "lineId": "line_64",
      "lineNumber": "64路",
      "lineName": "火车站-机场",
      "direction": 0,
      "startStop": "火车站",
      "endStop": "机场",
      "operationTime": {
        "startTime": "06:00",
        "endTime": "22:00"
      },
      "stops": [
        {
          "stopId": "stop_001",
          "stopName": "火车站",
          "sequence": 1,
          "location": {
            "latitude": 22.547,
            "longitude": 114.085
          }
        }
      ]
    }
  ]
}
```

### 2.2 消息队列规范

#### 2.2.1 实时数据消息格式
```json
// Topic: realtime.gps.updates
{
  "messageId": "msg_123456",
  "source": "platform_a",
  "timestamp": "2025-01-19T12:00:00Z",
  "eventType": "gps_update",
  "data": {
    "busId": "bus_001",
    "lineId": "line_64",
    "location": {
      "latitude": 22.547123,
      "longitude": 114.085456,
      "accuracy": 5.2
    },
    "speed": 25.5,
    "direction": 180,
    "timestamp": "2025-01-19T11:59:30Z",
    "status": "normal"
  }
}
```

#### 2.2.2 状态变更消息格式
```json
// Topic: system.status.changes
{
  "messageId": "msg_123457",
  "source": "platform_a",
  "timestamp": "2025-01-19T12:00:00Z",
  "eventType": "line_status_change",
  "data": {
    "lineId": "line_64",
    "oldStatus": "normal",
    "newStatus": "delayed",
    "reason": "traffic_jam",
    "estimatedDelay": 300
  }
}
```

## 3. 独立同步服务设计

### 3.1 同步服务架构
```
┌─────────────────────────────────────────────────────────────┐
│              平台A数据同步服务                               │
│                                                             │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│  │  数据采集   │ -> │  数据转换   │ -> │  数据推送   │     │
│  │   模块      │    │   模块      │    │   模块      │     │
│  └─────────────┘    └─────────────┘    └─────────────┘     │
│                                                             │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│  │  健康检查   │    │  错误重试   │    │  监控告警   │     │
│  │   模块      │    │   模块      │    │   模块      │     │
│  └─────────────┘    └─────────────┘    └─────────────┘     │
└─────────────────────────────────────────────────────────────┘
```

### 3.2 .NET实现示例

#### 3.2.1 数据同步服务基类
```csharp
public abstract class BaseSyncService : BackgroundService
{
    protected readonly ILogger<BaseSyncService> _logger;
    protected readonly HttpClient _httpClient;
    protected readonly IConfiguration _config;
    protected readonly IMessageProducer _messageProducer;

    public abstract string PlatformName { get; }
    
    protected BaseSyncService(
        ILogger<BaseSyncService> logger,
        HttpClient httpClient,
        IConfiguration config,
        IMessageProducer messageProducer)
    {
        _logger = logger;
        _httpClient = httpClient;
        _config = config;
        _messageProducer = messageProducer;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                // 采集数据
                var rawData = await CollectDataAsync();
                
                // 转换数据
                var standardData = TransformData(rawData);
                
                // 推送数据
                await PushDataAsync(standardData);
                
                // 等待下次同步
                await Task.Delay(GetSyncInterval(), stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Data sync failed for platform {Platform}", PlatformName);
                await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
            }
        }
    }

    protected abstract Task<object> CollectDataAsync();
    protected abstract StandardBusData[] TransformData(object rawData);
    protected abstract TimeSpan GetSyncInterval();

    protected async Task PushDataAsync(StandardBusData[] data)
    {
        // HTTP API推送
        var request = new SyncRequest
        {
            Source = PlatformName,
            Timestamp = DateTime.UtcNow,
            Data = data
        };

        var response = await _httpClient.PostAsJsonAsync("/api/v1/sync/realtime/gps", request);
        response.EnsureSuccessStatusCode();

        // 消息队列推送（可选）
        foreach (var item in data)
        {
            await _messageProducer.PublishAsync("realtime.gps.updates", new
            {
                MessageId = Guid.NewGuid().ToString(),
                Source = PlatformName,
                Timestamp = DateTime.UtcNow,
                EventType = "gps_update",
                Data = item
            });
        }
    }
}
```

#### 3.2.2 具体平台实现
```csharp
public class PlatformASyncService : BaseSyncService
{
    public override string PlatformName => "platform_a";

    public PlatformASyncService(
        ILogger<PlatformASyncService> logger,
        HttpClient httpClient,
        IConfiguration config,
        IMessageProducer messageProducer)
        : base(logger, httpClient, config, messageProducer)
    {
    }

    protected override async Task<object> CollectDataAsync()
    {
        var apiUrl = _config["PlatformA:ApiUrl"];
        var apiKey = _config["PlatformA:ApiKey"];
        
        _httpClient.DefaultRequestHeaders.Authorization = 
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", apiKey);
            
        var response = await _httpClient.GetAsync($"{apiUrl}/vehicles/realtime");
        response.EnsureSuccessStatusCode();
        
        return await response.Content.ReadFromJsonAsync<PlatformAGpsData[]>();
    }

    protected override StandardBusData[] TransformData(object rawData)
    {
        var platformData = (PlatformAGpsData[])rawData;
        
        return platformData.Select(item => new StandardBusData
        {
            BusId = item.VehicleId,
            LineId = item.RouteId,
            Location = new Location
            {
                Latitude = item.Lat,
                Longitude = item.Lng,
                Accuracy = item.Accuracy ?? 10
            },
            Speed = item.SpeedKmh,
            Direction = item.Heading,
            Timestamp = DateTimeOffset.FromUnixTimeSeconds(item.Timestamp).DateTime,
            Status = MapStatus(item.Status),
            PassengerCount = item.PassengerCount
        }).ToArray();
    }

    protected override TimeSpan GetSyncInterval()
    {
        return TimeSpan.FromSeconds(_config.GetValue<int>("PlatformA:SyncIntervalSeconds", 30));
    }

    private BusStatus MapStatus(string platformStatus)
    {
        return platformStatus?.ToLower() switch
        {
            "normal" => BusStatus.Normal,
            "delayed" => BusStatus.Delayed,
            "breakdown" => BusStatus.Breakdown,
            _ => BusStatus.Unknown
        };
    }
}
```

### 3.3 项目结构
```
BusSystem.DataSync.PlatformA/
├── Services/
│   ├── PlatformASyncService.cs
│   ├── PlatformAApiClient.cs
│   └── DataTransformService.cs
├── Models/
│   ├── PlatformAModels.cs
│   └── StandardModels.cs
├── Configuration/
│   └── PlatformAConfig.cs
├── Program.cs
├── appsettings.json
└── Dockerfile

BusSystem.DataSync.Hisense/
├── Services/
│   ├── HisenseSyncService.cs
│   ├── HisenseSoapClient.cs
│   └── DataTransformService.cs
├── Models/
│   ├── HisenseModels.cs
│   └── StandardModels.cs
└── ...
```

## 4. 主系统接收端设计

### 4.1 数据接收API
```csharp
[ApiController]
[Route("api/v1/sync")]
public class DataSyncController : ControllerBase
{
    private readonly IDataSyncService _dataSyncService;
    private readonly ILogger<DataSyncController> _logger;

    [HttpPost("realtime/gps")]
    public async Task<IActionResult> ReceiveRealtimeGps([FromBody] SyncRequest<StandardBusData[]> request)
    {
        try
        {
            // 验证数据源
            if (!await _dataSyncService.ValidateSourceAsync(request.Source))
            {
                return Unauthorized($"Invalid data source: {request.Source}");
            }

            // 验证数据格式
            if (!_dataSyncService.ValidateData(request.Data))
            {
                return BadRequest("Invalid data format");
            }

            // 处理数据
            var result = await _dataSyncService.ProcessRealtimeGpsAsync(request);

            return Ok(new
            {
                Success = true,
                ProcessedCount = result.ProcessedCount,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process realtime GPS data from {Source}", request.Source);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("basic/lines")]
    public async Task<IActionResult> ReceiveBasicLines([FromBody] SyncRequest<StandardLineData[]> request)
    {
        // 类似的基础数据处理逻辑
        var result = await _dataSyncService.ProcessBasicLinesAsync(request);
        return Ok(result);
    }
}
```

### 4.2 消息队列消费者
```csharp
public class RealtimeDataConsumer : BackgroundService
{
    private readonly IMessageConsumer _messageConsumer;
    private readonly IDataSyncService _dataSyncService;

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        await _messageConsumer.SubscribeAsync("realtime.gps.updates", async (message) =>
        {
            var gpsUpdate = JsonSerializer.Deserialize<GpsUpdateMessage>(message);
            await _dataSyncService.ProcessSingleGpsUpdateAsync(gpsUpdate);
        }, stoppingToken);
    }
}
```

## 5. 部署和运维

### 5.1 Docker部署
```yaml
# docker-compose.yml
version: '3.8'
services:
  # 主系统
  bus-system-main:
    image: bus-system/main:latest
    ports:
      - "5000:80"
    environment:
      - ConnectionStrings__DefaultConnection=...
      - Redis__ConnectionString=...

  # 平台A同步服务
  platform-a-sync:
    image: bus-system/platform-a-sync:latest
    environment:
      - PlatformA__ApiUrl=https://platform-a.com/api
      - PlatformA__ApiKey=your-api-key
      - BusSystem__ApiUrl=http://bus-system-main/api/v1/sync
      - BusSystem__Token=sync-token
    restart: unless-stopped

  # 海信同步服务
  hisense-sync:
    image: bus-system/hisense-sync:latest
    environment:
      - Hisense__WsdlUrl=https://hisense.com/service.wsdl
      - Hisense__Username=user
      - Hisense__Password=pass
    restart: unless-stopped

  # 消息队列
  rabbitmq:
    image: rabbitmq:3-management
    ports:
      - "5672:5672"
      - "15672:15672"
```

### 5.2 监控和告警
```csharp
public class SyncServiceHealthCheck : IHealthCheck
{
    private readonly PlatformASyncService _syncService;

    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            var lastSyncTime = await _syncService.GetLastSyncTimeAsync();
            var timeSinceLastSync = DateTime.UtcNow - lastSyncTime;

            if (timeSinceLastSync > TimeSpan.FromMinutes(5))
            {
                return HealthCheckResult.Unhealthy($"Last sync was {timeSinceLastSync.TotalMinutes:F1} minutes ago");
            }

            return HealthCheckResult.Healthy("Sync service is running normally");
        }
        catch (Exception ex)
        {
            return HealthCheckResult.Unhealthy("Sync service health check failed", ex);
        }
    }
}
```

## 6. 优势总结

### 6.1 架构优势
- **完全解耦**: 主系统与数据同步完全分离
- **独立维护**: 每个平台的同步服务可以独立开发和部署
- **灵活扩展**: 新增平台只需开发独立的同步服务
- **故障隔离**: 某个平台的同步服务故障不影响其他服务

### 6.2 运维优势
- **独立部署**: 可以根据需要独立扩缩容
- **版本管理**: 每个同步服务可以独立版本管理
- **监控告警**: 可以针对每个平台设置独立的监控
- **故障恢复**: 单个服务故障恢复更快

这种架构设计更加符合微服务的理念，您觉得如何？

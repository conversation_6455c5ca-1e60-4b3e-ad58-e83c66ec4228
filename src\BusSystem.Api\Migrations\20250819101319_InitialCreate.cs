﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using NetTopologySuite.Geometries;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace BusSystem.Api.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterDatabase()
                .Annotation("Npgsql:PostgresExtension:postgis", ",,");

            migrationBuilder.CreateTable(
                name: "bus_lines",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    LineNumber = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    LineName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    StartStopName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    EndStopName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Direction = table.Column<int>(type: "integer", nullable: false),
                    route_geometry = table.Column<LineString>(type: "geometry(LINESTRING,4326)", nullable: true),
                    OperationStartTime = table.Column<TimeSpan>(type: "interval", nullable: true),
                    OperationEndTime = table.Column<TimeSpan>(type: "interval", nullable: true),
                    PeakInterval = table.Column<int>(type: "integer", nullable: true),
                    NormalInterval = table.Column<int>(type: "integer", nullable: true),
                    TicketPrice = table.Column<decimal>(type: "numeric(4,2)", nullable: true),
                    TotalDistance = table.Column<decimal>(type: "numeric(8,2)", nullable: true),
                    TotalStops = table.Column<int>(type: "integer", nullable: true),
                    CompanyId = table.Column<int>(type: "integer", nullable: true),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_bus_lines", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "bus_stops",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    StopName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    StopCode = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    location = table.Column<Point>(type: "geometry(POINT,4326)", nullable: false),
                    Address = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    District = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    Facilities = table.Column<string>(type: "jsonb", nullable: true),
                    StopType = table.Column<int>(type: "integer", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_bus_stops", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "vehicles",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    VehicleNumber = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    PlateNumber = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    LineId = table.Column<int>(type: "integer", nullable: false),
                    VehicleType = table.Column<int>(type: "integer", nullable: false),
                    Capacity = table.Column<int>(type: "integer", nullable: true),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    CompanyId = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_vehicles", x => x.Id);
                    table.ForeignKey(
                        name: "FK_vehicles_bus_lines_LineId",
                        column: x => x.LineId,
                        principalTable: "bus_lines",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "line_stops",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    LineId = table.Column<int>(type: "integer", nullable: false),
                    StopId = table.Column<int>(type: "integer", nullable: false),
                    SequenceNumber = table.Column<int>(type: "integer", nullable: false),
                    DistanceFromStart = table.Column<decimal>(type: "numeric(8,2)", nullable: true),
                    EstimatedTime = table.Column<int>(type: "integer", nullable: true),
                    IsKeyStop = table.Column<bool>(type: "boolean", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_line_stops", x => x.Id);
                    table.ForeignKey(
                        name: "FK_line_stops_bus_lines_LineId",
                        column: x => x.LineId,
                        principalTable: "bus_lines",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_line_stops_bus_stops_StopId",
                        column: x => x.StopId,
                        principalTable: "bus_stops",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "idx_lines_company",
                table: "bus_lines",
                column: "CompanyId");

            migrationBuilder.CreateIndex(
                name: "idx_lines_number",
                table: "bus_lines",
                column: "LineNumber");

            migrationBuilder.CreateIndex(
                name: "idx_lines_status",
                table: "bus_lines",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "idx_stops_code",
                table: "bus_stops",
                column: "StopCode");

            migrationBuilder.CreateIndex(
                name: "idx_stops_district",
                table: "bus_stops",
                column: "District");

            migrationBuilder.CreateIndex(
                name: "idx_stops_location",
                table: "bus_stops",
                column: "location");

            migrationBuilder.CreateIndex(
                name: "idx_stops_name",
                table: "bus_stops",
                column: "StopName");

            migrationBuilder.CreateIndex(
                name: "idx_line_stops_line_sequence",
                table: "line_stops",
                columns: new[] { "LineId", "SequenceNumber" });

            migrationBuilder.CreateIndex(
                name: "idx_line_stops_stop",
                table: "line_stops",
                column: "StopId");

            migrationBuilder.CreateIndex(
                name: "idx_vehicles_line",
                table: "vehicles",
                column: "LineId");

            migrationBuilder.CreateIndex(
                name: "idx_vehicles_number",
                table: "vehicles",
                column: "VehicleNumber");

            migrationBuilder.CreateIndex(
                name: "idx_vehicles_status",
                table: "vehicles",
                column: "Status");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "line_stops");

            migrationBuilder.DropTable(
                name: "vehicles");

            migrationBuilder.DropTable(
                name: "bus_stops");

            migrationBuilder.DropTable(
                name: "bus_lines");
        }
    }
}

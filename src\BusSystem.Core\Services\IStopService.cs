using BusSystem.Core.Entities;
using BusSystem.Shared.Models.Common;

namespace BusSystem.Core.Services;

/// <summary>
/// 站点服务接口
/// </summary>
public interface IStopService
{
    /// <summary>
    /// 获取站点详情
    /// </summary>
    Task<BusStop?> GetStopAsync(int stopId);
    
    /// <summary>
    /// 根据站点编码获取站点
    /// </summary>
    Task<BusStop?> GetStopByCodeAsync(string stopCode);
    
    /// <summary>
    /// 获取站点及其线路信息
    /// </summary>
    Task<BusStop?> GetStopWithLinesAsync(int stopId);
    
    /// <summary>
    /// 搜索站点
    /// </summary>
    Task<IEnumerable<BusStop>> SearchStopsAsync(string keyword);
    
    /// <summary>
    /// 获取附近的站点
    /// </summary>
    Task<IEnumerable<BusStop>> GetNearbyStopsAsync(double longitude, double latitude, double radiusMeters = 500);
    
    /// <summary>
    /// 获取所有活跃站点
    /// </summary>
    Task<IEnumerable<BusStop>> GetActiveStopsAsync();
    
    /// <summary>
    /// 分页获取站点
    /// </summary>
    Task<PagedResult<BusStop>> GetStopsPagedAsync(int pageNumber, int pageSize, string? keyword = null);
    
    /// <summary>
    /// 创建站点
    /// </summary>
    Task<BusStop> CreateStopAsync(BusStop stop);
    
    /// <summary>
    /// 更新站点
    /// </summary>
    Task<BusStop> UpdateStopAsync(BusStop stop);
    
    /// <summary>
    /// 删除站点
    /// </summary>
    Task DeleteStopAsync(int stopId);
    
    /// <summary>
    /// 获取站点统计信息
    /// </summary>
    Task<object> GetStopStatsAsync(int stopId);
}

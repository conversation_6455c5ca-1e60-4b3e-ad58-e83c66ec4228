# 实时公交系统后端开发启动指南

## 1. 项目背景

### 1.1 项目概述
开发一个实时公交查询系统，为用户提供：
- 附近站点查询
- 实时车辆位置追踪
- 到站时间预测
- 线路信息查询

### 1.2 技术栈确认
- **后端**: .NET 8 + ASP.NET Core + C#
- **数据库**: PostgreSQL 15 + PostGIS + TimescaleDB + Redis Geo
- **架构**: 独立同步服务 + 统一接口规范
- **部署**: Docker + Kubernetes

### 1.3 架构设计
采用**独立同步服务模式**：
- **主系统**: 负责业务逻辑和API服务
- **同步服务**: 独立的调度平台数据同步服务
- **统一接口**: HTTP API + 消息队列进行数据传输

## 2. 开发环境配置

### 2.1 Docker Compose 环境
创建 `docker-compose.dev.yml` 用于本地开发：

```yaml
version: '3.8'
services:
  # PostgreSQL + PostGIS
  postgres:
    image: postgis/postgis:15-3.3
    environment:
      - POSTGRES_DB=BusSystem
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=*********
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql

  # TimescaleDB
  timescaledb:
    image: timescale/timescaledb:latest-pg15
    environment:
      - POSTGRES_DB=BusSystem
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=*********
    ports:
      - "5433:5432"
    volumes:
      - timescaledb_data:/var/lib/postgresql/data

  # Redis
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  timescaledb_data:
  redis_data:
```

### 2.2 连接字符串配置
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Port=5432;Database=BusSystem;Username=postgres;Password=*********",
    "TimescaleConnection": "Host=localhost;Port=5433;Database=BusSystem;Username=postgres;Password=*********",
    "Redis": "localhost:6379"
  }
}
```

## 3. 项目结构规划

### 3.1 解决方案结构
```
BusSystem/
├── src/
│   ├── BusSystem.Api/              # API网关层
│   ├── BusSystem.Core/             # 核心业务层
│   ├── BusSystem.Infrastructure/   # 基础设施层
│   ├── BusSystem.Shared/          # 共享模型和工具
│   └── BusSystem.Tests/           # 单元测试
├── scripts/
│   ├── init-db.sql                # 数据库初始化脚本
│   └── docker-compose.dev.yml     # 开发环境配置
└── docs/                          # 项目文档
```

### 3.2 核心项目说明
- **BusSystem.Api**: ASP.NET Core Web API项目，包含控制器和中间件
- **BusSystem.Core**: 业务逻辑层，包含服务接口和实现
- **BusSystem.Infrastructure**: 数据访问层，包含Repository和外部服务
- **BusSystem.Shared**: 共享的模型、常量、扩展方法等

## 4. 第一阶段开发任务

### 4.1 优先级1：项目基础搭建（第1天）
1. **创建解决方案和项目结构**
   - 创建.NET 8解决方案
   - 添加各个项目
   - 配置项目依赖关系

2. **配置基础设施**
   - 添加必要的NuGet包
   - 配置依赖注入
   - 配置日志和配置系统

3. **数据库连接配置**
   - 配置Entity Framework Core
   - 配置PostgreSQL和Redis连接
   - 测试数据库连接

### 4.2 优先级2：数据访问层（第2-3天）
1. **Entity模型定义**
   - Line（线路）
   - Stop（站点）
   - Vehicle（车辆）
   - BusPosition（GPS位置）

2. **DbContext配置**
   - PostgreSQL DbContext
   - TimescaleDB DbContext
   - Redis配置

3. **Repository模式实现**
   - 基础Repository接口和实现
   - 具体业务Repository

### 4.3 优先级3：核心业务服务（第4-5天）
1. **LineService**: 线路管理服务
2. **StopService**: 站点管理服务
3. **RealtimeService**: 实时数据处理服务
4. **GeoService**: 地理位置查询服务（Redis Geo）

### 4.4 优先级4：API接口（第6-7天）
1. **基础CRUD API**
   - 线路查询API
   - 站点查询API
   - 附近站点查询API

2. **数据同步API**
   - GPS数据接收接口
   - 基础数据同步接口

## 5. 关键技术要点

### 5.1 Redis Geo使用
```csharp
// 添加站点位置
await _redis.GeoAddAsync("bus_stops", longitude, latitude, stopId);

// 查询附近站点
var nearbyStops = await _redis.GeoRadiusAsync("bus_stops", longitude, latitude, 500, GeoUnit.Meters);
```

### 5.2 TimescaleDB时序数据
```csharp
// GPS轨迹表（时序表）
[Table("bus_positions")]
public class BusPosition
{
    public DateTime Timestamp { get; set; }
    public string BusId { get; set; }
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public double Speed { get; set; }
}
```

### 5.3 PostGIS空间查询
```csharp
// 使用PostGIS进行空间查询
var nearbyStops = await _context.Stops
    .Where(s => s.Location.Distance(userLocation) <= 500)
    .OrderBy(s => s.Location.Distance(userLocation))
    .ToListAsync();
```

## 6. 开发规范

### 6.1 代码规范
- 使用C# 12语法特性
- 遵循SOLID原则
- 使用异步编程模式
- 统一的错误处理和日志记录

### 6.2 API设计规范
- RESTful API设计
- 统一的响应格式
- 适当的HTTP状态码
- API版本控制

### 6.3 数据库规范
- 统一的命名约定
- 适当的索引设计
- 数据迁移脚本管理

## 7. 测试策略

### 7.1 单元测试
- 业务逻辑单元测试
- Repository层测试
- 服务层测试

### 7.2 集成测试
- API接口测试
- 数据库集成测试
- Redis集成测试

## 8. 下一步行动

### 8.1 立即开始 ✅ 已完成
1. ✅ 启动Docker Compose环境
2. ✅ 创建.NET解决方案结构
3. ✅ 配置基础的依赖注入和配置

### 8.2 第一个里程碑（1周后）✅ 已完成
- ✅ 完成基础项目结构
- ✅ 实现线路和站点的基础CRUD
- ✅ 实现Redis Geo附近站点查询
- ✅ 完成基础API接口

## 9. 重要文档引用

开发过程中需要参考的关键文档：
- `数据库设计.md` - 数据表结构设计
- `接口设计.md` - API接口规范
- `后端架构设计.md` - 架构设计详情
- `数据同步架构设计.md` - 同步服务设计

## 10. 联系方式和注意事项

### 10.1 开发注意事项
- 优先实现核心功能，后续迭代优化
- 保持代码简洁和可测试性
- 及时编写单元测试
- 注意性能优化，特别是地理查询部分

### 10.2 常见问题
- PostgreSQL连接问题：检查Docker容器状态
- Redis连接问题：确认端口映射正确
- Entity Framework迁移：使用Code First方式

这个指南包含了开始.NET后端开发所需的所有关键信息。新会话中的AI助手可以直接基于这个指南开始工作。

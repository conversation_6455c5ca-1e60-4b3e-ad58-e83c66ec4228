using System.Net.WebSockets;

namespace BusSystem.Core.Services;

/// <summary>
/// 实时推送通知服务接口
/// </summary>
public interface INotificationService
{
    /// <summary>
    /// 添加WebSocket连接
    /// </summary>
    Task<string> AddConnectionAsync(WebSocket webSocket, string? userId = null);
    
    /// <summary>
    /// 移除WebSocket连接
    /// </summary>
    Task RemoveConnectionAsync(string connectionId);
    
    /// <summary>
    /// 订阅线路实时信息
    /// </summary>
    Task SubscribeToLineAsync(string connectionId, int lineId);
    
    /// <summary>
    /// 取消订阅线路
    /// </summary>
    Task UnsubscribeFromLineAsync(string connectionId, int lineId);
    
    /// <summary>
    /// 订阅站点实时信息
    /// </summary>
    Task SubscribeToStopAsync(string connectionId, int stopId);
    
    /// <summary>
    /// 取消订阅站点
    /// </summary>
    Task UnsubscribeFromStopAsync(string connectionId, int stopId);
    
    /// <summary>
    /// 订阅附近区域的实时信息
    /// </summary>
    Task SubscribeToAreaAsync(string connectionId, double longitude, double latitude, double radiusMeters);
    
    /// <summary>
    /// 推送车辆位置更新
    /// </summary>
    Task NotifyVehiclePositionUpdateAsync(int vehicleId, object positionData);
    
    /// <summary>
    /// 推送到站预测更新
    /// </summary>
    Task NotifyArrivalPredictionUpdateAsync(int stopId, object predictionData);
    
    /// <summary>
    /// 推送线路状态更新
    /// </summary>
    Task NotifyLineStatusUpdateAsync(int lineId, object statusData);
    
    /// <summary>
    /// 发送消息到指定连接
    /// </summary>
    Task SendMessageToConnectionAsync(string connectionId, object message);
    
    /// <summary>
    /// 广播消息到所有连接
    /// </summary>
    Task BroadcastMessageAsync(object message);
    
    /// <summary>
    /// 获取连接统计信息
    /// </summary>
    Task<object> GetConnectionStatsAsync();
    
    /// <summary>
    /// 清理无效连接
    /// </summary>
    Task CleanupInvalidConnectionsAsync();
}

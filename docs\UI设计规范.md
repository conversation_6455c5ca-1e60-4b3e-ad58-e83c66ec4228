# 实时公交系统UI设计规范

## 1. 设计原则

### 1.1 核心原则
- **简洁明了**: 界面简洁，信息层次清晰
- **实时性强**: 重要信息（到站时间）突出显示
- **易于操作**: 符合移动端操作习惯
- **一致性**: 保持整个应用的视觉和交互一致性

### 1.2 设计目标
- 用户能在3秒内找到所需信息
- 关键操作不超过2次点击
- 适配各种移动设备屏幕

## 2. 视觉设计规范

### 2.1 色彩系统

#### 主色调
- **品牌绿**: #00C853 (主要用于标题栏、按钮、强调信息)
- **深绿**: #00A843 (按钮按下状态)

#### 功能色彩
- **成功/正常**: #00C853 (到站时间、正常状态)
- **警告/延误**: #FF9800 (延误提醒、注意信息)
- **错误/停运**: #F44336 (异常状态、错误提示)
- **信息**: #2196F3 (一般信息提示)

#### 中性色彩
- **主文字**: #212121
- **副文字**: #757575
- **辅助文字**: #BDBDBD
- **分割线**: #E0E0E0
- **背景**: #FAFAFA
- **卡片背景**: #FFFFFF

### 2.2 字体规范

#### 字体大小
- **大标题**: 20px (页面主标题)
- **中标题**: 18px (卡片标题、线路号)
- **小标题**: 16px (站点名称)
- **正文**: 14px (描述信息)
- **辅助文字**: 12px (距离、时间等)
- **到站时间**: 18px (重要数据突出显示)

#### 字体粗细
- **粗体**: 600 (标题、重要信息)
- **中等**: 500 (次要标题)
- **常规**: 400 (正文内容)

### 2.3 间距系统
- **超小间距**: 4px
- **小间距**: 8px
- **标准间距**: 12px
- **中等间距**: 16px
- **大间距**: 24px
- **超大间距**: 32px

### 2.4 圆角规范
- **卡片圆角**: 12px
- **按钮圆角**: 8px
- **小组件圆角**: 6px
- **图标圆角**: 4px

## 3. 组件设计规范

### 3.1 卡片组件
```
┌─────────────────────────────────┐
│  站点名称                 距离   │
│  ─────────────────────────────  │
│  🚌 64路  开往终点站             │
│      8分钟 · 2站    15分钟 · 5站 │
│  🚌 E38路 开往终点站             │
│      13分钟 · 6站   30分钟 · 7站 │
└─────────────────────────────────┘
```

**设计要求**:
- 背景色: #FFFFFF
- 圆角: 12px
- 内边距: 16px
- 外边距: 12px
- 阴影: 0 2px 8px rgba(0,0,0,0.1)

### 3.2 线路信息组件
```
🚌 [线路号] [线路描述]
   [到站时间] · [站数]  [到站时间] · [站数]
```

**设计要求**:
- 线路号: 18px, 粗体, 主色调
- 到站时间: 16px, 粗体, 绿色
- 站数: 14px, 常规, 灰色

### 3.3 虚拟站牌组件
```
○ 已过站点 (灰色)
● 当前站点 (绿色，大圆点)
○ 未到站点 (浅灰色)
🚌 车辆位置 (动态图标)
```

### 3.4 按钮组件

#### 主要按钮
- 背景色: #00C853
- 文字色: #FFFFFF
- 高度: 44px
- 圆角: 8px
- 按下状态: #00A843

#### 次要按钮
- 背景色: #F5F5F5
- 文字色: #757575
- 边框: 1px solid #E0E0E0

#### 文字按钮
- 背景色: 透明
- 文字色: #00C853
- 无边框

## 4. 页面布局规范

### 4.1 主页布局
```
┌─────────────────────────────────┐
│ 🔙 公交                    ⚙️ │ ← 标题栏 (56px)
│ 🔍 搜线路、站点、目的地          │ ← 搜索栏 (48px)
├─────────────────────────────────┤
│ 📍 我的关注                     │ ← 快捷入口 (可选)
├─────────────────────────────────┤
│ [站点卡片1]                     │
│ [站点卡片2]                     │ ← 内容区域
│ [站点卡片3]                     │
│ ...                             │
└─────────────────────────────────┘
```

### 4.2 线路详情页布局
```
┌─────────────────────────────────┐
│ 🔙 🏠 62路                      │ ← 标题栏
│ 钢铁街公交站 ← 左岸红郡公交站     │ ← 线路信息
├─────────────────────────────────┤
│                                 │
│        [地图区域]               │ ← 地图 (40%高度)
│                                 │
├─────────────────────────────────┤
│ 📍 福华三路口            ⭐     │ ← 当前站点
│ 5分钟·2站  25分钟·9站  49分钟    │ ← 到站信息
├─────────────────────────────────┤
│ [虚拟站牌]                      │ ← 站点列表
│ ○ 站点1                         │
│ ● 当前站点                      │
│ ○ 站点3                         │
├─────────────────────────────────┤
│ 🔄 刷新  ↕️ 上下行  💳 公交卡    │ ← 底部操作栏
└─────────────────────────────────┘
```

### 4.3 站点信息页布局
```
┌─────────────────────────────────┐
│ 🔙 🏠 大中华国际广场        ↔️  │ ← 标题栏+切换站台
├─────────────────────────────────┤
│                                 │
│        [地图区域]               │ ← 地图 (30%高度)
│                                 │
├─────────────────────────────────┤
│ [线路卡片1] M390路               │
│ [线路卡片2] 60路                │ ← 线路列表
│ [线路卡片3] E38路               │
│ [线路卡片4] M500路              │
├─────────────────────────────────┤
│ 🔄 刷新        📍 去这里        │ ← 底部操作栏
└─────────────────────────────────┘
```

## 5. 交互设计规范

### 5.1 手势交互
- **下拉刷新**: 在列表顶部下拉触发数据刷新
- **点击**: 卡片、按钮点击有0.2s过渡动画
- **滑动**: 虚拟站牌支持垂直滑动查看
- **缩放**: 地图支持双指缩放

### 5.2 状态反馈
- **加载状态**: 骨架屏 + 旋转加载图标
- **空数据**: 友好的空状态插图 + 提示文字
- **网络错误**: 错误图标 + 重试按钮
- **定位失败**: 定位图标 + 手动定位按钮

### 5.3 动画效果
- **页面切换**: 滑动过渡，300ms
- **数据更新**: 淡入淡出，200ms
- **按钮点击**: 缩放效果，150ms
- **卡片展开**: 高度变化，250ms

## 6. 响应式设计

### 6.1 屏幕适配
- **小屏幕** (< 375px): 减少内边距，调整字体大小
- **标准屏幕** (375px - 414px): 标准设计
- **大屏幕** (> 414px): 增加内边距，保持内容居中

### 6.2 横屏适配
- 地图区域扩大显示
- 虚拟站牌改为横向布局
- 保持核心功能可用性

## 7. 无障碍设计

### 7.1 可访问性
- 所有交互元素最小点击区域44px×44px
- 重要信息支持语音播报
- 颜色信息不作为唯一区分方式
- 支持系统字体大小设置

### 7.2 老年人友好
- 字体可放大至120%
- 高对比度模式支持
- 简化操作流程
- 重要按钮加大尺寸

## 8. 设计交付规范

### 8.1 设计稿要求
- 使用2x分辨率设计
- 标注所有间距、字体、颜色
- 提供各种状态的设计稿
- 包含交互说明

### 8.2 切图规范
- 图标提供1x、2x、3x三种规格
- 使用SVG格式（优先）或PNG格式
- 命名规范：功能_状态_尺寸.格式

### 8.3 设计验收
- 与设计稿像素级一致
- 交互动画流畅自然
- 各种设备适配良好
- 用户体验测试通过

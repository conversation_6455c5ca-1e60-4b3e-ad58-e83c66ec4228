# 单按钮智能跳转方案（简化版）

基于你的最终需求，我设计了一个极简但功能完整的方案，只需一个按钮，根据传入参数自动适应不同跳转需求，无参数时自动隐藏按钮。

## 完整实现代码

```html
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>智能跳转H5</title>
  <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
  <style>
    #smart-jump-btn {
      display: none; /* 默认隐藏 */
      margin: 20px auto;
      padding: 10px 20px;
      background-color: #07C160;
      color: white;
      border: none;
      border-radius: 4px;
      font-size: 16px;
      cursor: pointer;
    }
  </style>
</head>
<body>
  <h1>我的H5页面</h1>
  <p>这里是页面主要内容...</p>
  
  <!-- 唯一智能跳转按钮 -->
  <button id="smart-jump-btn"></button>

  <script>
    // 环境检测
    const isInMiniProgram = () => {
      const ua = navigator.userAgent.toLowerCase();
      return (ua.includes('micromessenger') && ua.includes('miniprogram')) || 
             (typeof wx !== 'undefined' && wx.miniProgram);
    };

    // 解析URL参数
    const getUrlParam = (name) => {
      const params = new URLSearchParams(window.location.search);
      return params.get(name) ? decodeURIComponent(params.get(name)) : null;
    };

    // 初始化按钮
    const initSmartButton = () => {
      const btn = document.getElementById('smart-jump-btn');
      const btnText = getUrlParam('btn_text') || '返回';
      const miniProgramPath = getUrlParam('mini_path');
      const webUrl = getUrlParam('web_url');
      
      // 设置按钮文本
      btn.textContent = btnText;
      
      // 根据参数设置跳转行为
      if (isInMiniProgram() && miniProgramPath) {
        // 小程序环境且有小程序路径参数
        btn.style.display = 'block';
        btn.addEventListener('click', () => {
          wx.miniProgram.navigateTo({ url: miniProgramPath });
        });
      } else if (webUrl) {
        // 有网页跳转地址
        btn.style.display = 'block';
        btn.addEventListener('click', () => {
          window.location.href = webUrl;
        });
      }
      // 无有效参数时保持按钮隐藏
    };

    // 页面加载后初始化
    document.addEventListener('DOMContentLoaded', initSmartButton);
  </script>
</body>
</html>
```

## 调用方式示例

### 1. 跳转回小程序页面
```
https://your-h5.com/page?btn_text=返回首页&mini_path=/pages/home/<USER>
```

### 2. 跳转到外部网页
```
https://your-h5.com/page?btn_text=访问官网&web_url=https://example.com
```

### 3. 无跳转参数（按钮自动隐藏）
```
https://your-h5.com/page
```

## 方案特点

1. **极简设计**：只有一个按钮，干净利落
2. **智能适应**：
   - 小程序环境优先使用`mini_path`跳转
   - 普通环境使用`web_url`跳转
   - 无参数时自动隐藏按钮
3. **完全可定制**：
   - 通过`btn_text`参数自定义按钮文字
   - 通过CSS可轻松修改按钮样式
4. **安全可靠**：
   - 自动处理URL编码解码
   - 严格的参数检查

## 参数说明

| 参数名 | 描述 | 示例 |
|--------|------|------|
| `btn_text` | 按钮显示文字 | `返回首页` |
| `mini_path` | 小程序跳转路径 | `/pages/home/<USER>
| `web_url` | 网页跳转地址 | `https://example.com` |

## 注意事项

1. 小程序跳转需要H5域名已配置在小程序后台的"业务域名"中
2. 网页跳转地址需要包含协议头（http/https）
3. 按钮样式可以通过修改CSS进一步自定义

这个方案完美符合你"单一按钮、自动适应、无参隐藏"的核心需求，同时保持了足够的灵活性和健壮性。
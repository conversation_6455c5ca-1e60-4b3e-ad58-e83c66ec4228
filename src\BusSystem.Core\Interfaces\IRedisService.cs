using StackExchange.Redis;

namespace BusSystem.Core.Interfaces;

/// <summary>
/// Redis服务接口
/// </summary>
public interface IRedisService
{
    /// <summary>
    /// 获取字符串值
    /// </summary>
    Task<string?> GetStringAsync(string key);
    
    /// <summary>
    /// 设置字符串值
    /// </summary>
    Task<bool> SetStringAsync(string key, string value, TimeSpan? expiry = null);
    
    /// <summary>
    /// 获取对象
    /// </summary>
    Task<T?> GetAsync<T>(string key) where T : class;
    
    /// <summary>
    /// 设置对象
    /// </summary>
    Task<bool> SetAsync<T>(string key, T value, TimeSpan? expiry = null) where T : class;
    
    /// <summary>
    /// 删除键
    /// </summary>
    Task<bool> DeleteAsync(string key);
    
    /// <summary>
    /// 检查键是否存在
    /// </summary>
    Task<bool> ExistsAsync(string key);
    
    /// <summary>
    /// 添加地理位置
    /// </summary>
    Task<bool> GeoAddAsync(string key, double longitude, double latitude, string member);
    
    /// <summary>
    /// 查询附近的地理位置
    /// </summary>
    Task<GeoRadiusResult[]> GeoRadiusAsync(string key, double longitude, double latitude, double radius, GeoUnit unit = GeoUnit.Meters);
}

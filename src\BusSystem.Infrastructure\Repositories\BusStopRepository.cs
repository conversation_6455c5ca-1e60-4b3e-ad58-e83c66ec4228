using Microsoft.EntityFrameworkCore;
using NetTopologySuite.Geometries;
using BusSystem.Core.Entities;
using BusSystem.Core.Interfaces;
using BusSystem.Infrastructure.Data;

namespace BusSystem.Infrastructure.Repositories;

/// <summary>
/// 公交站点Repository实现
/// </summary>
public class BusStopRepository : Repository<BusStop>, IBusStopRepository
{
    public BusStopRepository(BusSystemDbContext context) : base(context)
    {
    }

    public async Task<BusStop?> GetByStopCodeAsync(string stopCode)
    {
        return await _dbSet
            .FirstOrDefaultAsync(x => x.StopCode == stopCode);
    }

    public async Task<IEnumerable<BusStop>> SearchByNameAsync(string name)
    {
        return await _dbSet
            .Where(x => x.StopName.Contains(name))
            .OrderBy(x => x.StopName)
            .ToListAsync();
    }

    public async Task<IEnumerable<BusStop>> GetNearbyStopsAsync(double longitude, double latitude, double radiusMeters)
    {
        // 创建查询点
        var geometryFactory = new GeometryFactory(new PrecisionModel(), 4326);
        var queryPoint = geometryFactory.CreatePoint(new Coordinate(longitude, latitude));

        return await _dbSet
            .Where(x => x.Location.Distance(queryPoint) <= radiusMeters)
            .OrderBy(x => x.Location.Distance(queryPoint))
            .ToListAsync();
    }

    public async Task<BusStop?> GetWithLinesAsync(int stopId)
    {
        return await _dbSet
            .Include(x => x.LineStops)
                .ThenInclude(ls => ls.Line)
            .FirstOrDefaultAsync(x => x.Id == stopId);
    }

    public async Task<IEnumerable<BusStop>> GetByDistrictAsync(string district)
    {
        return await _dbSet
            .Where(x => x.District == district)
            .OrderBy(x => x.StopName)
            .ToListAsync();
    }

    public async Task<IEnumerable<BusStop>> GetByTypeAsync(int stopType)
    {
        return await _dbSet
            .Where(x => x.StopType == stopType)
            .OrderBy(x => x.StopName)
            .ToListAsync();
    }

    public async Task<IEnumerable<BusStop>> GetActiveAsync()
    {
        return await _dbSet
            .Where(x => x.Status == 1)
            .OrderBy(x => x.StopName)
            .ToListAsync();
    }
}

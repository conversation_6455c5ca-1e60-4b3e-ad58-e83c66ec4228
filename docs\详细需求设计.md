# 实时公交系统详细需求设计文档

## 1. 项目概述

### 1.1 项目背景
实时公交系统旨在为用户提供准确、及时的公交信息查询服务，包括实时到站时间、线路信息、站点信息等，提升用户出行体验。

### 1.2 项目目标
- 提供实时准确的公交到站信息
- 支持线路和站点信息查询
- 提供直观的地图展示功能
- 构建易用的移动端H5应用
- 建立完善的运营后台管理系统

### 1.3 用户群体
- **主要用户**: 日常使用公交出行的市民
- **管理用户**: 公交运营管理人员
- **系统管理员**: 技术运维人员

## 2. 功能需求详细设计

### 2.1 H5前端应用

#### 2.1.1 实时公交主页面
**功能描述**: 用户进入应用后的首页，展示附近站点的实时公交信息

**详细需求**:
1. **页面头部**
   - 应用标题"公交"
   - 右上角功能按钮（设置、帮助等）
   - 搜索框：支持"搜线路、站点、目的地"
   - 我的关注入口（后期扩展）

2. **附近站点展示**
   - 基于用户当前位置，自动获取附近500米内的公交站点
   - 按距离远近排序显示站点列表
   - 每个站点显示：
     - 站点名称（如"大中华国际广场"）
     - 距离用户的步行距离
     - 经过该站点的线路列表

3. **线路实时信息展示**
   - 每条线路显示格式：
     - 线路号（如"64路"、"E38路"）
     - 线路描述（起终点站信息）
     - 最近两班车的到站信息：
       - 第一班车：到站时间（如"8分钟"）+ 剩余站数（如"2站"）
       - 第二班车：到站时间（如"15分钟"）+ 剩余站数（如"5站"）
     - 车辆状态标识（正常/延误/拥挤）

4. **打车出行模块**
   - 显示当前位置到常用目的地的打车信息
   - 预估费用和时间
   - 跳转到第三方打车应用

5. **交互功能**
   - 点击线路 → 跳转线路详情页
   - 点击站点名称 → 跳转站点信息页
   - 下拉刷新更新数据
   - 支持定位权限申请和处理
   - 支持手动切换定位

**界面要求**:
- 采用卡片式设计，每个站点一个卡片
- 线路信息用不同颜色区分状态（绿色正常、橙色延误、红色拥挤）
- 到站时间采用醒目的数字显示
- 支持骨架屏加载效果
- 网络异常和定位失败的友好提示

#### 2.1.2 线路详情页面
**功能描述**: 展示特定公交线路的详细信息和实时状态

**详细需求**:
1. **页面头部**
   - 返回按钮和首页按钮
   - 线路号大标题（如"62路"）
   - 线路描述（起终点站，如"钢铁街的公交车站 ← 左岸红郡的公交车站"）
   - 线路基础信息：票价、里程等

2. **地图展示区域**
   - 集成高德地图，显示完整线路走向
   - 实时显示车辆位置（用车辆图标标注）
   - 标注所有站点位置
   - 高亮显示当前选中站点
   - 显示到达当前站点的预计时间（如"5分钟"）
   - 支持地图缩放和拖拽

3. **当前站点信息卡片**
   - 显示当前选中站点名称（如"福华三路口"）
   - 关注按钮（收藏该站点）
   - 最近几班车的到站预测：
     - 第一班：到站时间（如"5分钟"）+ 剩余站数（如"2站"）
     - 第二班：到站时间（如"25分钟"）+ 剩余站数（如"9站"）
     - 第三班：到站时间（如"49分钟"）+ 剩余站数（如"站"）
   - 车辆状态图标和拥挤程度

4. **虚拟站牌**
   - 垂直展示所有站点列表
   - 每个站点显示：
     - 站点名称
     - 站点状态（已过站/当前站/未到站）
     - 车辆位置标识（小车图标）
   - 当前站点高亮显示
   - 支持点击切换查看不同站点
   - 显示线路方向（上行/下行）

5. **底部功能区**
   - 刷新按钮（更新实时数据）
   - 上下行切换按钮
   - 查看公交卡按钮（跳转到公交卡相关功能）

6. **发车时刻信息**
   - 显示发车间隔信息
   - 运营时间段
   - 票价信息

**界面要求**:
- 地图和虚拟站牌上下布局
- 当前站点信息用卡片形式悬浮显示
- 到站时间用醒目颜色和大字体显示
- 车辆位置实时更新（30秒间隔）
- 支持下拉刷新
- 虚拟站牌支持滚动查看所有站点
- 已过站点用灰色显示，未到站点用正常颜色

#### 2.1.3 站点信息页面
**功能描述**: 展示特定公交站点的详细信息和经过该站点的所有线路实时信息

**详细需求**:
1. **页面头部**
   - 返回按钮和首页按钮
   - 站点名称大标题（如"大中华国际广场"）
   - 站点描述信息
   - 切换站台按钮（如显示对面站台）

2. **地图展示区域**
   - 集成高德地图，标注当前站点位置
   - 显示同名站点的其他站台位置（对向站台）
   - 周边重要设施标注（地铁站、商场、学校等）
   - 支持地图缩放和拖拽
   - 当前站点用特殊图标高亮显示

3. **经过线路信息列表**
   - 显示所有经过该站点的公交线路
   - 每条线路显示：
     - 线路号（如"M390路"、"60路"、"E38路"、"M500路"）
     - 线路描述（起终点站信息）
     - 最近两班车的实时到站信息：
       - 第一班：到站时间（如"6分钟"）+ 剩余站数（如"2站"）
       - 第二班：到站时间（如"4分钟"）+ 剩余站数（如"1站"）
     - 车辆状态和拥挤程度标识

4. **站台切换功能**
   - 同一站点的不同方向站台切换
   - 清晰标识当前查看的站台方向
   - 显示对向站台的距离信息
   - 支持一键切换到对向站台

5. **底部功能区**
   - 刷新按钮（更新实时数据）
   - 去这里按钮（路线规划功能，后期扩展）
   - 查看公交卡按钮

6. **交互功能**
   - 点击任意线路 → 跳转到该线路详情页
   - 支持下拉刷新更新所有线路的实时信息
   - 支持按到站时间排序显示线路

**界面要求**:
- 地图和线路列表上下布局
- 线路信息采用卡片式设计，每条线路一个卡片
- 到站时间用醒目的绿色数字显示
- 支持骨架屏加载效果
- 线路状态用不同颜色标识（正常/延误/停运）
- 实时数据每30秒自动更新
- 站台切换按钮明显易用

#### 2.1.4 搜索页面
**功能描述**: 提供线路和站点的搜索功能

**详细需求**:
1. **搜索功能**
   - 支持线路号精确搜索
   - 支持线路名称模糊搜索
   - 支持站点名称模糊搜索
   - 实时搜索建议

2. **搜索结果**
   - 线路搜索结果直接跳转线路详情页
   - 站点搜索结果跳转站点信息页
   - 搜索结果按相关度排序

3. **搜索历史**
   - 保存最近10次搜索记录
   - 支持清空搜索历史
   - 点击历史记录快速搜索

**界面要求**:
- 搜索框自动获取焦点
- 支持语音输入（后期扩展）
- 无结果时显示友好提示

### 2.2 后端API服务

#### 2.2.1 数据接口需求
1. **实时公交数据接口**
   - 获取附近站点接口
   - 获取线路详情接口
   - 获取站点信息接口
   - 获取实时车辆位置接口
   - 搜索接口（线路/站点）

2. **地图服务接口**
   - 地理位置转换
   - 路径规划
   - 地图瓦片服务

3. **系统接口**
   - 用户定位记录
   - 错误日志收集
   - 性能监控数据

#### 2.2.2 数据处理需求
1. **实时数据处理**
   - 公交GPS数据实时接收和处理
   - 到站时间算法优化
   - 数据缓存策略

2. **数据同步**
   - 与公交公司数据系统对接
   - 线路和站点基础数据同步
   - 异常数据处理和修正

### 2.3 运营后台系统

#### 2.3.1 数据管理
1. **基础数据管理**
   - 线路信息管理（增删改查）
   - 站点信息管理
   - 车辆信息管理
   - 时刻表管理

2. **实时数据监控**
   - 车辆位置实时监控
   - 数据接收状态监控
   - 异常数据报警

#### 2.3.2 系统管理
1. **用户访问统计**
   - 页面访问量统计
   - 用户行为分析
   - 热门线路/站点统计

2. **系统配置**
   - 接口参数配置
   - 缓存策略配置
   - 告警规则配置

## 3. 非功能需求

### 3.1 性能需求
- 页面加载时间 < 3秒
- 接口响应时间 < 1秒
- 支持并发用户数 > 1000
- 数据更新频率：30秒

### 3.2 可用性需求
- 系统可用性 > 99%
- 7x24小时服务
- 故障恢复时间 < 30分钟

### 3.3 兼容性需求
- 支持主流移动浏览器
- 兼容Android 5.0+、iOS 10+
- 支持微信内置浏览器

### 3.4 安全需求
- HTTPS加密传输
- 接口访问频率限制
- 用户隐私保护

## 4. 约束条件

### 4.1 技术约束
- 必须集成高德地图API
- 需要获取用户位置权限
- 依赖公交公司数据接口

### 4.2 业务约束
- 数据准确性依赖第三方
- 服务范围限定在特定城市
- 需要持续的数据维护

## 5. UI设计规范

### 5.1 色彩规范
- **主色调**: 绿色系（#00C853 或类似）
- **辅助色**:
  - 正常状态：绿色（#00C853）
  - 延误状态：橙色（#FF9800）
  - 停运/异常：红色（#F44336）
  - 文字主色：深灰色（#333333）
  - 文字辅色：浅灰色（#666666）
- **背景色**: 浅灰色（#F5F5F5）

### 5.2 字体规范
- **主标题**: 18px，粗体
- **副标题**: 16px，中等粗细
- **正文**: 14px，常规
- **辅助信息**: 12px，常规
- **到站时间**: 16px，粗体，绿色

### 5.3 组件规范
- **卡片圆角**: 8px
- **按钮圆角**: 6px
- **间距标准**: 8px、12px、16px、24px
- **阴影**: 轻微阴影效果，增强层次感

### 5.4 交互规范
- **点击反馈**: 0.2s过渡动画
- **加载状态**: 骨架屏 + 加载动画
- **下拉刷新**: 标准下拉刷新交互
- **页面切换**: 滑动过渡动画

## 6. 数据展示规范

### 6.1 时间显示规范
- **即将到站**: "即将到站"（红色）
- **1分钟内**: "1分钟"（橙色）
- **正常时间**: "X分钟"（绿色）
- **长时间**: "XX分钟"（灰色）
- **无数据**: "暂无数据"（灰色）

### 6.2 距离显示规范
- **站数**: "X站"
- **步行距离**: "XXXm"
- **较远距离**: "X.Xkm"

### 6.3 状态标识规范
- **车辆状态**:
  - 正常运行：绿色圆点
  - 延误：橙色圆点
  - 故障/停运：红色圆点
- **拥挤程度**:
  - 空闲：绿色
  - 适中：橙色
  - 拥挤：红色

## 7. 验收标准

### 7.1 功能验收
- 所有页面功能正常运行
- 数据显示准确及时
- 交互体验流畅
- 地图功能正常
- 实时数据更新及时

### 7.2 性能验收
- 满足性能指标要求
- 通过压力测试
- 移动端体验良好
- 页面加载速度符合要求

### 7.3 质量验收
- 代码质量符合规范
- 通过安全测试
- 文档完整准确
- UI设计符合规范
- 用户体验测试通过

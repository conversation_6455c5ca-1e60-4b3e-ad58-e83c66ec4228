﻿// <auto-generated />
using System;
using BusSystem.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using NetTopologySuite.Geometries;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace BusSystem.Api.Migrations
{
    [DbContext(typeof(BusSystemDbContext))]
    partial class BusSystemDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.8")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.HasPostgresExtension(modelBuilder, "postgis");
            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("BusSystem.Core.Entities.BusLine", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int?>("CompanyId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<int>("Direction")
                        .HasColumnType("integer");

                    b.Property<string>("EndStopName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("LineName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("LineNumber")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<int?>("NormalInterval")
                        .HasColumnType("integer");

                    b.Property<TimeSpan?>("OperationEndTime")
                        .HasColumnType("interval");

                    b.Property<TimeSpan?>("OperationStartTime")
                        .HasColumnType("interval");

                    b.Property<int?>("PeakInterval")
                        .HasColumnType("integer");

                    b.Property<LineString>("RouteGeometry")
                        .HasColumnType("geometry(LINESTRING,4326)")
                        .HasColumnName("route_geometry");

                    b.Property<string>("StartStopName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<decimal?>("TicketPrice")
                        .HasColumnType("decimal(4,2)");

                    b.Property<decimal?>("TotalDistance")
                        .HasColumnType("decimal(8,2)");

                    b.Property<int?>("TotalStops")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId")
                        .HasDatabaseName("idx_lines_company");

                    b.HasIndex("LineNumber")
                        .HasDatabaseName("idx_lines_number");

                    b.HasIndex("Status")
                        .HasDatabaseName("idx_lines_status");

                    b.ToTable("bus_lines", (string)null);
                });

            modelBuilder.Entity("BusSystem.Core.Entities.BusStop", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Address")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("District")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Facilities")
                        .HasColumnType("jsonb");

                    b.Property<Point>("Location")
                        .IsRequired()
                        .HasColumnType("geometry(POINT,4326)")
                        .HasColumnName("location");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("StopCode")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("StopName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("StopType")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.HasKey("Id");

                    b.HasIndex("District")
                        .HasDatabaseName("idx_stops_district");

                    b.HasIndex("Location")
                        .HasDatabaseName("idx_stops_location");

                    b.HasIndex("StopCode")
                        .HasDatabaseName("idx_stops_code");

                    b.HasIndex("StopName")
                        .HasDatabaseName("idx_stops_name");

                    b.ToTable("bus_stops", (string)null);
                });

            modelBuilder.Entity("BusSystem.Core.Entities.LineStop", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<decimal?>("DistanceFromStart")
                        .HasColumnType("decimal(8,2)");

                    b.Property<int?>("EstimatedTime")
                        .HasColumnType("integer");

                    b.Property<bool>("IsKeyStop")
                        .HasColumnType("boolean");

                    b.Property<int>("LineId")
                        .HasColumnType("integer");

                    b.Property<int>("SequenceNumber")
                        .HasColumnType("integer");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<int>("StopId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.HasKey("Id");

                    b.HasIndex("StopId")
                        .HasDatabaseName("idx_line_stops_stop");

                    b.HasIndex("LineId", "SequenceNumber")
                        .HasDatabaseName("idx_line_stops_line_sequence");

                    b.ToTable("line_stops", (string)null);
                });

            modelBuilder.Entity("BusSystem.Core.Entities.Vehicle", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int?>("Capacity")
                        .HasColumnType("integer");

                    b.Property<int?>("CompanyId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<int>("LineId")
                        .HasColumnType("integer");

                    b.Property<string>("PlateNumber")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("VehicleNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("VehicleType")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("LineId")
                        .HasDatabaseName("idx_vehicles_line");

                    b.HasIndex("Status")
                        .HasDatabaseName("idx_vehicles_status");

                    b.HasIndex("VehicleNumber")
                        .HasDatabaseName("idx_vehicles_number");

                    b.ToTable("vehicles", (string)null);
                });

            modelBuilder.Entity("BusSystem.Core.Entities.LineStop", b =>
                {
                    b.HasOne("BusSystem.Core.Entities.BusLine", "Line")
                        .WithMany("LineStops")
                        .HasForeignKey("LineId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BusSystem.Core.Entities.BusStop", "Stop")
                        .WithMany("LineStops")
                        .HasForeignKey("StopId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Line");

                    b.Navigation("Stop");
                });

            modelBuilder.Entity("BusSystem.Core.Entities.Vehicle", b =>
                {
                    b.HasOne("BusSystem.Core.Entities.BusLine", "Line")
                        .WithMany("Vehicles")
                        .HasForeignKey("LineId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Line");
                });

            modelBuilder.Entity("BusSystem.Core.Entities.BusLine", b =>
                {
                    b.Navigation("LineStops");

                    b.Navigation("Vehicles");
                });

            modelBuilder.Entity("BusSystem.Core.Entities.BusStop", b =>
                {
                    b.Navigation("LineStops");
                });
#pragma warning restore 612, 618
        }
    }
}

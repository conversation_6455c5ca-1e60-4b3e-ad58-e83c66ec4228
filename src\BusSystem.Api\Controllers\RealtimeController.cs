using Microsoft.AspNetCore.Mvc;
using BusSystem.Core.Services;
using BusSystem.Core.Entities;
using BusSystem.Shared.Models.Common;

namespace BusSystem.Api.Controllers;

/// <summary>
/// 实时数据控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class RealtimeController : ControllerBase
{
    private readonly IRealtimeService _realtimeService;
    private readonly IPredictionService _predictionService;
    private readonly ILogger<RealtimeController> _logger;

    public RealtimeController(
        IRealtimeService realtimeService,
        IPredictionService predictionService,
        ILogger<RealtimeController> logger)
    {
        _realtimeService = realtimeService;
        _predictionService = predictionService;
        _logger = logger;
    }

    /// <summary>
    /// 更新车辆位置
    /// </summary>
    /// <param name="position">车辆位置信息</param>
    [HttpPost("vehicle-position")]
    public async Task<ActionResult<ApiResponse<VehiclePosition>>> UpdateVehiclePosition([FromBody] VehiclePosition position)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<VehiclePosition>.Fail("请求参数无效"));
            }

            var result = await _realtimeService.UpdateVehiclePositionAsync(position);
            return Ok(ApiResponse<VehiclePosition>.Ok(result, "车辆位置更新成功"));
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ApiResponse<VehiclePosition>.Fail(ex.Message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新车辆位置失败");
            return StatusCode(500, ApiResponse<VehiclePosition>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 批量更新车辆位置
    /// </summary>
    /// <param name="positions">车辆位置信息列表</param>
    [HttpPost("vehicle-positions")]
    public async Task<ActionResult<ApiResponse<IEnumerable<VehiclePosition>>>> UpdateVehiclePositions([FromBody] IEnumerable<VehiclePosition> positions)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<IEnumerable<VehiclePosition>>.Fail("请求参数无效"));
            }

            var result = await _realtimeService.UpdateVehiclePositionsAsync(positions);
            return Ok(ApiResponse<IEnumerable<VehiclePosition>>.Ok(result, "批量更新车辆位置成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量更新车辆位置失败");
            return StatusCode(500, ApiResponse<IEnumerable<VehiclePosition>>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取车辆最新位置
    /// </summary>
    /// <param name="vehicleId">车辆ID</param>
    [HttpGet("vehicle/{vehicleId}/position")]
    public async Task<ActionResult<ApiResponse<VehiclePosition>>> GetVehicleLatestPosition(int vehicleId)
    {
        try
        {
            var position = await _realtimeService.GetVehicleLatestPositionAsync(vehicleId);
            if (position == null)
            {
                return NotFound(ApiResponse<VehiclePosition>.Fail("车辆位置信息不存在"));
            }

            return Ok(ApiResponse<VehiclePosition>.Ok(position));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取车辆最新位置失败，车辆ID: {VehicleId}", vehicleId);
            return StatusCode(500, ApiResponse<VehiclePosition>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取线路上所有车辆的实时位置
    /// </summary>
    /// <param name="lineId">线路ID</param>
    [HttpGet("line/{lineId}/vehicles")]
    public async Task<ActionResult<ApiResponse<IEnumerable<VehiclePosition>>>> GetLineRealtimePositions(int lineId)
    {
        try
        {
            var positions = await _realtimeService.GetLineRealtimePositionsAsync(lineId);
            return Ok(ApiResponse<IEnumerable<VehiclePosition>>.Ok(positions));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取线路实时位置失败，线路ID: {LineId}", lineId);
            return StatusCode(500, ApiResponse<IEnumerable<VehiclePosition>>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取附近的实时车辆
    /// </summary>
    /// <param name="longitude">经度</param>
    /// <param name="latitude">纬度</param>
    /// <param name="radius">搜索半径（米，默认1000米）</param>
    [HttpGet("nearby-vehicles")]
    public async Task<ActionResult<ApiResponse<IEnumerable<VehiclePosition>>>> GetNearbyVehicles(
        [FromQuery] double longitude,
        [FromQuery] double latitude,
        [FromQuery] double radius = 1000)
    {
        try
        {
            // 验证坐标范围
            if (longitude < -180 || longitude > 180 || latitude < -90 || latitude > 90)
            {
                return BadRequest(ApiResponse<IEnumerable<VehiclePosition>>.Fail("坐标参数无效"));
            }

            // 限制搜索半径
            if (radius <= 0 || radius > 5000) radius = 1000;

            var vehicles = await _realtimeService.GetNearbyVehiclesAsync(longitude, latitude, radius);
            return Ok(ApiResponse<IEnumerable<VehiclePosition>>.Ok(vehicles));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取附近车辆失败，位置: ({Longitude}, {Latitude}), 半径: {Radius}", 
                longitude, latitude, radius);
            return StatusCode(500, ApiResponse<IEnumerable<VehiclePosition>>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取车辆轨迹
    /// </summary>
    /// <param name="vehicleId">车辆ID</param>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    [HttpGet("vehicle/{vehicleId}/trajectory")]
    public async Task<ActionResult<ApiResponse<IEnumerable<VehiclePosition>>>> GetVehicleTrajectory(
        int vehicleId,
        [FromQuery] DateTime startTime,
        [FromQuery] DateTime endTime)
    {
        try
        {
            if (startTime >= endTime)
            {
                return BadRequest(ApiResponse<IEnumerable<VehiclePosition>>.Fail("时间范围无效"));
            }

            var trajectory = await _realtimeService.GetVehicleTrajectoryAsync(vehicleId, startTime, endTime);
            return Ok(ApiResponse<IEnumerable<VehiclePosition>>.Ok(trajectory));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取车辆轨迹失败，车辆ID: {VehicleId}", vehicleId);
            return StatusCode(500, ApiResponse<IEnumerable<VehiclePosition>>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取车辆实时状态
    /// </summary>
    /// <param name="vehicleId">车辆ID</param>
    [HttpGet("vehicle/{vehicleId}/status")]
    public async Task<ActionResult<ApiResponse<object>>> GetVehicleRealtimeStatus(int vehicleId)
    {
        try
        {
            var status = await _realtimeService.GetVehicleRealtimeStatusAsync(vehicleId);
            return Ok(ApiResponse<object>.Ok(status));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取车辆实时状态失败，车辆ID: {VehicleId}", vehicleId);
            return StatusCode(500, ApiResponse<object>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取线路实时统计
    /// </summary>
    /// <param name="lineId">线路ID</param>
    [HttpGet("line/{lineId}/stats")]
    public async Task<ActionResult<ApiResponse<object>>> GetLineRealtimeStats(int lineId)
    {
        try
        {
            var stats = await _realtimeService.GetLineRealtimeStatsAsync(lineId);
            return Ok(ApiResponse<object>.Ok(stats));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取线路实时统计失败，线路ID: {LineId}", lineId);
            return StatusCode(500, ApiResponse<object>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 预测车辆到站时间
    /// </summary>
    /// <param name="vehicleId">车辆ID</param>
    /// <param name="stopId">站点ID</param>
    [HttpGet("prediction/vehicle/{vehicleId}/stop/{stopId}")]
    public async Task<ActionResult<ApiResponse<object>>> PredictArrivalTime(int vehicleId, int stopId)
    {
        try
        {
            var prediction = await _predictionService.PredictArrivalTimeAsync(vehicleId, stopId);
            return Ok(ApiResponse<object>.Ok(prediction));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "预测到站时间失败，车辆ID: {VehicleId}, 站点ID: {StopId}", vehicleId, stopId);
            return StatusCode(500, ApiResponse<object>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取站点的所有车辆到站预测
    /// </summary>
    /// <param name="stopId">站点ID</param>
    [HttpGet("prediction/stop/{stopId}")]
    public async Task<ActionResult<ApiResponse<IEnumerable<object>>>> GetStopArrivalPredictions(int stopId)
    {
        try
        {
            var predictions = await _predictionService.GetStopArrivalPredictionsAsync(stopId);
            return Ok(ApiResponse<IEnumerable<object>>.Ok(predictions));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取站点到站预测失败，站点ID: {StopId}", stopId);
            return StatusCode(500, ApiResponse<IEnumerable<object>>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 预测车辆到下一站的时间
    /// </summary>
    /// <param name="vehicleId">车辆ID</param>
    [HttpGet("prediction/vehicle/{vehicleId}/next-stop")]
    public async Task<ActionResult<ApiResponse<object>>> PredictNextStopArrival(int vehicleId)
    {
        try
        {
            var prediction = await _predictionService.PredictNextStopArrivalAsync(vehicleId);
            return Ok(ApiResponse<object>.Ok(prediction));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "预测下一站到站时间失败，车辆ID: {VehicleId}", vehicleId);
            return StatusCode(500, ApiResponse<object>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 清理过期的位置数据
    /// </summary>
    /// <param name="retentionDays">保留天数</param>
    [HttpPost("cleanup")]
    public async Task<ActionResult<ApiResponse>> CleanupOldData([FromQuery] int retentionDays = 7)
    {
        try
        {
            if (retentionDays < 1 || retentionDays > 365)
            {
                return BadRequest(ApiResponse.Fail("保留天数必须在1-365之间"));
            }

            var retentionPeriod = TimeSpan.FromDays(retentionDays);
            await _realtimeService.CleanupOldPositionDataAsync(retentionPeriod);
            
            return Ok(ApiResponse.Ok($"清理完成，保留最近{retentionDays}天的数据"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理过期数据失败");
            return StatusCode(500, ApiResponse.Fail("服务器内部错误"));
        }
    }
}

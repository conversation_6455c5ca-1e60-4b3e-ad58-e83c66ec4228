using Microsoft.Extensions.Logging;
using BusSystem.Core.Entities;
using BusSystem.Core.Interfaces;
using BusSystem.Shared.Models.Common;

namespace BusSystem.Core.Services;

/// <summary>
/// 站点服务实现
/// </summary>
public class StopService : IStopService
{
    private readonly IBusStopRepository _stopRepository;
    private readonly ILogger<StopService> _logger;

    public StopService(
        IBusStopRepository stopRepository,
        ILogger<StopService> logger)
    {
        _stopRepository = stopRepository;
        _logger = logger;
    }

    public async Task<BusStop?> GetStopAsync(int stopId)
    {
        try
        {
            return await _stopRepository.GetByIdAsync(stopId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取站点失败，站点ID: {StopId}", stopId);
            throw;
        }
    }

    public async Task<BusStop?> GetStopByCodeAsync(string stopCode)
    {
        try
        {
            return await _stopRepository.GetByStopCodeAsync(stopCode);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据站点编码获取站点失败，站点编码: {StopCode}", stopCode);
            throw;
        }
    }

    public async Task<BusStop?> GetStopWithLinesAsync(int stopId)
    {
        try
        {
            return await _stopRepository.GetWithLinesAsync(stopId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取站点及线路信息失败，站点ID: {StopId}", stopId);
            throw;
        }
    }

    public async Task<IEnumerable<BusStop>> SearchStopsAsync(string keyword)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(keyword))
            {
                return await _stopRepository.GetActiveAsync();
            }
            return await _stopRepository.SearchByNameAsync(keyword.Trim());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "搜索站点失败，关键词: {Keyword}", keyword);
            throw;
        }
    }

    public async Task<IEnumerable<BusStop>> GetNearbyStopsAsync(double longitude, double latitude, double radiusMeters = 500)
    {
        try
        {
            return await _stopRepository.GetNearbyStopsAsync(longitude, latitude, radiusMeters);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取附近站点失败，经度: {Longitude}, 纬度: {Latitude}, 半径: {Radius}", 
                longitude, latitude, radiusMeters);
            throw;
        }
    }

    public async Task<IEnumerable<BusStop>> GetActiveStopsAsync()
    {
        try
        {
            return await _stopRepository.GetActiveAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取活跃站点失败");
            throw;
        }
    }

    public async Task<PagedResult<BusStop>> GetStopsPagedAsync(int pageNumber, int pageSize, string? keyword = null)
    {
        try
        {
            if (!string.IsNullOrWhiteSpace(keyword))
            {
                return await _stopRepository.GetPagedAsync(
                    pageNumber, pageSize,
                    x => x.StopName.Contains(keyword) || 
                         (x.StopCode != null && x.StopCode.Contains(keyword)) ||
                         (x.Address != null && x.Address.Contains(keyword)),
                    x => x.StopName);
            }
            
            return await _stopRepository.GetPagedAsync(
                pageNumber, pageSize,
                orderBy: x => x.StopName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "分页获取站点失败，页码: {PageNumber}, 页大小: {PageSize}, 关键词: {Keyword}", 
                pageNumber, pageSize, keyword);
            throw;
        }
    }

    public async Task<BusStop> CreateStopAsync(BusStop stop)
    {
        try
        {
            // 验证站点编码是否已存在（如果提供了编码）
            if (!string.IsNullOrWhiteSpace(stop.StopCode))
            {
                var existingStop = await _stopRepository.GetByStopCodeAsync(stop.StopCode);
                if (existingStop != null)
                {
                    throw new InvalidOperationException($"站点编码 {stop.StopCode} 已存在");
                }
            }

            var result = await _stopRepository.AddAsync(stop);
            _logger.LogInformation("创建站点成功，站点ID: {StopId}, 站点名称: {StopName}", 
                result.Id, result.StopName);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建站点失败，站点名称: {StopName}", stop.StopName);
            throw;
        }
    }

    public async Task<BusStop> UpdateStopAsync(BusStop stop)
    {
        try
        {
            var existingStop = await _stopRepository.GetByIdAsync(stop.Id);
            if (existingStop == null)
            {
                throw new InvalidOperationException($"站点不存在，ID: {stop.Id}");
            }

            var result = await _stopRepository.UpdateAsync(stop);
            _logger.LogInformation("更新站点成功，站点ID: {StopId}, 站点名称: {StopName}", 
                result.Id, result.StopName);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新站点失败，站点ID: {StopId}", stop.Id);
            throw;
        }
    }

    public async Task DeleteStopAsync(int stopId)
    {
        try
        {
            var stop = await _stopRepository.GetWithLinesAsync(stopId);
            if (stop == null)
            {
                throw new InvalidOperationException($"站点不存在，ID: {stopId}");
            }

            // 检查是否有关联的线路
            if (stop.LineStops?.Any() == true)
            {
                throw new InvalidOperationException("无法删除站点，存在关联的线路");
            }

            await _stopRepository.DeleteAsync(stop);
            _logger.LogInformation("删除站点成功，站点ID: {StopId}, 站点名称: {StopName}", 
                stopId, stop.StopName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除站点失败，站点ID: {StopId}", stopId);
            throw;
        }
    }

    public async Task<object> GetStopStatsAsync(int stopId)
    {
        try
        {
            var stop = await _stopRepository.GetWithLinesAsync(stopId);
            if (stop == null)
            {
                throw new InvalidOperationException($"站点不存在，ID: {stopId}");
            }

            var lineStops = stop.LineStops?.ToList() ?? new List<LineStop>();
            var lines = lineStops.Select(ls => ls.Line).Where(l => l != null).ToList();

            return new
            {
                StopId = stopId,
                StopName = stop.StopName,
                StopCode = stop.StopCode,
                Location = new
                {
                    Longitude = stop.Longitude,
                    Latitude = stop.Latitude
                },
                TotalLines = lines.Count,
                ActiveLines = lines.Count(l => l.Status == 1),
                StopType = stop.StopType,
                District = stop.District,
                Address = stop.Address
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取站点统计信息失败，站点ID: {StopId}", stopId);
            throw;
        }
    }
}

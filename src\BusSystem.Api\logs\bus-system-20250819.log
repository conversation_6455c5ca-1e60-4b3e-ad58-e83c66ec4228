[2025-08-19 18:06:24.397 +08:00 INF] Now listening on: http://localhost:5205 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-19 18:06:24.426 +08:00 INF] Application started. Press Ctrl+C to shut down. {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-19 18:06:24.428 +08:00 INF] Hosting environment: Development {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-19 18:06:24.429 +08:00 INF] Content root path: E:\Coding\Solution\实时公交\src\BusSystem.Api {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-19 18:13:40.608 +08:00 ERR] Failed executing DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE vehicles (
    "Id" integer GENERATED BY DEFAULT AS IDENTITY,
    "VehicleNumber" character varying(50) NOT NULL,
    "PlateNumber" character varying(20),
    "LineId" integer NOT NULL,
    "VehicleType" integer NOT NULL,
    "Capacity" integer,
    "Status" integer NOT NULL,
    "CompanyId" integer,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    CONSTRAINT "PK_vehicles" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_vehicles_bus_lines_LineId" FOREIGN KEY ("LineId") REFERENCES bus_lines ("Id") ON DELETE RESTRICT
); {"EventId":{"Id":20102,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandError"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command"}
[2025-08-19 18:14:22.406 +08:00 INF] Now listening on: http://localhost:5205 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-19 18:14:22.431 +08:00 INF] Application started. Press Ctrl+C to shut down. {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-19 18:14:22.432 +08:00 INF] Hosting environment: Development {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-19 18:14:22.433 +08:00 INF] Content root path: E:\Coding\Solution\实时公交\src\BusSystem.Api {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-19 18:14:44.004 +08:00 WRN] Failed to determine the https port for redirect. {"EventId":{"Id":3,"Name":"FailedToDeterminePort"},"SourceContext":"Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware","RequestId":"0HNEV14RS24BA:00000001","RequestPath":"/api/health/detailed","ConnectionId":"0HNEV14RS24BA"}
[2025-08-19 18:14:44.130 +08:00 INF] 详细健康检查完成 - 整体状态: Healthy {"SourceContext":"BusSystem.Api.Controllers.HealthController","ActionId":"54a471a0-c338-466c-b511-79cb7fe9e34d","ActionName":"BusSystem.Api.Controllers.HealthController.GetDetailed (BusSystem.Api)","RequestId":"0HNEV14RS24BA:00000001","RequestPath":"/api/health/detailed","ConnectionId":"0HNEV14RS24BA"}
[2025-08-19 18:14:44.170 +08:00 INF] HTTP GET /api/health/detailed responded 200 in 166.1111 ms {"SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware"}
[2025-08-19 18:21:03.725 +08:00 INF] Now listening on: http://localhost:5205 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-19 18:21:03.750 +08:00 INF] Application started. Press Ctrl+C to shut down. {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-19 18:21:03.752 +08:00 INF] Hosting environment: Development {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-19 18:21:03.753 +08:00 INF] Content root path: E:\Coding\Solution\实时公交\src\BusSystem.Api {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-19 18:21:24.686 +08:00 WRN] Failed to determine the https port for redirect. {"EventId":{"Id":3,"Name":"FailedToDeterminePort"},"SourceContext":"Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware","RequestId":"0HNEV18J9A36T:00000001","RequestPath":"/api/health/detailed","ConnectionId":"0HNEV18J9A36T"}
[2025-08-19 18:21:24.789 +08:00 INF] 详细健康检查完成 - 整体状态: Healthy {"SourceContext":"BusSystem.Api.Controllers.HealthController","ActionId":"e5d626f5-b09c-4b70-a152-af23581a2be3","ActionName":"BusSystem.Api.Controllers.HealthController.GetDetailed (BusSystem.Api)","RequestId":"0HNEV18J9A36T:00000001","RequestPath":"/api/health/detailed","ConnectionId":"0HNEV18J9A36T"}
[2025-08-19 18:21:24.820 +08:00 INF] HTTP GET /api/health/detailed responded 200 in 134.6174 ms {"SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware"}
[2025-08-19 18:21:30.778 +08:00 INF] HTTP GET /api/lines/active responded 200 in 993.3820 ms {"SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware"}
[2025-08-19 18:21:35.308 +08:00 INF] HTTP GET /api/stops/active responded 200 in 51.5064 ms {"SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware"}

using BusSystem.Core.Entities;

namespace BusSystem.Core.Interfaces;

/// <summary>
/// 车辆Repository接口
/// </summary>
public interface IVehicleRepository : IRepository<Vehicle>
{
    /// <summary>
    /// 根据车辆编号获取车辆
    /// </summary>
    Task<Vehicle?> GetByVehicleNumberAsync(string vehicleNumber);
    
    /// <summary>
    /// 根据车牌号获取车辆
    /// </summary>
    Task<Vehicle?> GetByPlateNumberAsync(string plateNumber);
    
    /// <summary>
    /// 根据线路ID获取车辆
    /// </summary>
    Task<IEnumerable<Vehicle>> GetByLineIdAsync(int lineId);
    
    /// <summary>
    /// 获取车辆及其线路信息
    /// </summary>
    Task<Vehicle?> GetWithLineAsync(int vehicleId);
    
    /// <summary>
    /// 获取运营中的车辆
    /// </summary>
    Task<IEnumerable<Vehicle>> GetActiveAsync();
    
    /// <summary>
    /// 根据车辆类型获取车辆
    /// </summary>
    Task<IEnumerable<Vehicle>> GetByTypeAsync(int vehicleType);
    
    /// <summary>
    /// 根据公司ID获取车辆
    /// </summary>
    Task<IEnumerable<Vehicle>> GetByCompanyAsync(int companyId);
}

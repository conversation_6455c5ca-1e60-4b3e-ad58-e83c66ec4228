using Microsoft.AspNetCore.Mvc;
using BusSystem.Core.Services;
using BusSystem.Core.Entities;
using BusSystem.Shared.Models.Common;
using BusSystem.Shared.Constants;

namespace BusSystem.Api.Controllers;

/// <summary>
/// 公交站点控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class StopsController : ControllerBase
{
    private readonly IStopService _stopService;
    private readonly ILogger<StopsController> _logger;

    public StopsController(IStopService stopService, ILogger<StopsController> logger)
    {
        _stopService = stopService;
        _logger = logger;
    }

    /// <summary>
    /// 获取站点详情
    /// </summary>
    /// <param name="id">站点ID</param>
    [HttpGet("{id}")]
    public async Task<ActionResult<ApiResponse<BusStop>>> GetStop(int id)
    {
        try
        {
            var stop = await _stopService.GetStopAsync(id);
            if (stop == null)
            {
                return NotFound(ApiResponse<BusStop>.Fail("站点不存在"));
            }

            return Ok(ApiResponse<BusStop>.Ok(stop));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取站点详情失败，站点ID: {StopId}", id);
            return StatusCode(500, ApiResponse<BusStop>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取站点及其线路信息
    /// </summary>
    /// <param name="id">站点ID</param>
    [HttpGet("{id}/lines")]
    public async Task<ActionResult<ApiResponse<BusStop>>> GetStopWithLines(int id)
    {
        try
        {
            var stop = await _stopService.GetStopWithLinesAsync(id);
            if (stop == null)
            {
                return NotFound(ApiResponse<BusStop>.Fail("站点不存在"));
            }

            return Ok(ApiResponse<BusStop>.Ok(stop));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取站点及线路信息失败，站点ID: {StopId}", id);
            return StatusCode(500, ApiResponse<BusStop>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 搜索站点
    /// </summary>
    /// <param name="keyword">搜索关键词</param>
    [HttpGet("search")]
    public async Task<ActionResult<ApiResponse<IEnumerable<BusStop>>>> SearchStops([FromQuery] string? keyword)
    {
        try
        {
            var stops = await _stopService.SearchStopsAsync(keyword ?? string.Empty);
            return Ok(ApiResponse<IEnumerable<BusStop>>.Ok(stops));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "搜索站点失败，关键词: {Keyword}", keyword);
            return StatusCode(500, ApiResponse<IEnumerable<BusStop>>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取附近的站点
    /// </summary>
    /// <param name="longitude">经度</param>
    /// <param name="latitude">纬度</param>
    /// <param name="radius">搜索半径（米，默认500米）</param>
    [HttpGet("nearby")]
    public async Task<ActionResult<ApiResponse<IEnumerable<BusStop>>>> GetNearbyStops(
        [FromQuery] double longitude,
        [FromQuery] double latitude,
        [FromQuery] double radius = 500)
    {
        try
        {
            // 验证坐标范围
            if (longitude < -180 || longitude > 180 || latitude < -90 || latitude > 90)
            {
                return BadRequest(ApiResponse<IEnumerable<BusStop>>.Fail("坐标参数无效"));
            }

            // 限制搜索半径
            if (radius <= 0 || radius > 5000) radius = 500;

            var stops = await _stopService.GetNearbyStopsAsync(longitude, latitude, radius);
            return Ok(ApiResponse<IEnumerable<BusStop>>.Ok(stops));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取附近站点失败，经度: {Longitude}, 纬度: {Latitude}, 半径: {Radius}", 
                longitude, latitude, radius);
            return StatusCode(500, ApiResponse<IEnumerable<BusStop>>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 分页获取站点
    /// </summary>
    /// <param name="pageNumber">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="keyword">搜索关键词</param>
    [HttpGet]
    public async Task<ActionResult<ApiResponse<PagedResult<BusStop>>>> GetStops(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = SystemConstants.DefaultPageSize,
        [FromQuery] string? keyword = null)
    {
        try
        {
            // 验证分页参数
            if (pageNumber < 1) pageNumber = 1;
            if (pageSize < 1 || pageSize > SystemConstants.MaxPageSize) 
                pageSize = SystemConstants.DefaultPageSize;

            var result = await _stopService.GetStopsPagedAsync(pageNumber, pageSize, keyword);
            return Ok(ApiResponse<PagedResult<BusStop>>.Ok(result));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "分页获取站点失败，页码: {PageNumber}, 页大小: {PageSize}, 关键词: {Keyword}", 
                pageNumber, pageSize, keyword);
            return StatusCode(500, ApiResponse<PagedResult<BusStop>>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取活跃站点
    /// </summary>
    [HttpGet("active")]
    public async Task<ActionResult<ApiResponse<IEnumerable<BusStop>>>> GetActiveStops()
    {
        try
        {
            var stops = await _stopService.GetActiveStopsAsync();
            return Ok(ApiResponse<IEnumerable<BusStop>>.Ok(stops));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取活跃站点失败");
            return StatusCode(500, ApiResponse<IEnumerable<BusStop>>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 根据站点编码获取站点
    /// </summary>
    /// <param name="stopCode">站点编码</param>
    [HttpGet("code/{stopCode}")]
    public async Task<ActionResult<ApiResponse<BusStop>>> GetStopByCode(string stopCode)
    {
        try
        {
            var stop = await _stopService.GetStopByCodeAsync(stopCode);
            if (stop == null)
            {
                return NotFound(ApiResponse<BusStop>.Fail("站点不存在"));
            }

            return Ok(ApiResponse<BusStop>.Ok(stop));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据站点编码获取站点失败，站点编码: {StopCode}", stopCode);
            return StatusCode(500, ApiResponse<BusStop>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取站点统计信息
    /// </summary>
    /// <param name="id">站点ID</param>
    [HttpGet("{id}/stats")]
    public async Task<ActionResult<ApiResponse<object>>> GetStopStats(int id)
    {
        try
        {
            var stats = await _stopService.GetStopStatsAsync(id);
            return Ok(ApiResponse<object>.Ok(stats));
        }
        catch (InvalidOperationException ex)
        {
            return NotFound(ApiResponse<object>.Fail(ex.Message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取站点统计信息失败，站点ID: {StopId}", id);
            return StatusCode(500, ApiResponse<object>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 创建站点
    /// </summary>
    /// <param name="stop">站点信息</param>
    [HttpPost]
    public async Task<ActionResult<ApiResponse<BusStop>>> CreateStop([FromBody] BusStop stop)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<BusStop>.Fail("请求参数无效"));
            }

            var result = await _stopService.CreateStopAsync(stop);
            return CreatedAtAction(nameof(GetStop), new { id = result.Id }, 
                ApiResponse<BusStop>.Ok(result, "站点创建成功"));
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ApiResponse<BusStop>.Fail(ex.Message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建站点失败，站点名称: {StopName}", stop.StopName);
            return StatusCode(500, ApiResponse<BusStop>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 更新站点
    /// </summary>
    /// <param name="id">站点ID</param>
    /// <param name="stop">站点信息</param>
    [HttpPut("{id}")]
    public async Task<ActionResult<ApiResponse<BusStop>>> UpdateStop(int id, [FromBody] BusStop stop)
    {
        try
        {
            if (id != stop.Id)
            {
                return BadRequest(ApiResponse<BusStop>.Fail("站点ID不匹配"));
            }

            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<BusStop>.Fail("请求参数无效"));
            }

            var result = await _stopService.UpdateStopAsync(stop);
            return Ok(ApiResponse<BusStop>.Ok(result, "站点更新成功"));
        }
        catch (InvalidOperationException ex)
        {
            return NotFound(ApiResponse<BusStop>.Fail(ex.Message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新站点失败，站点ID: {StopId}", id);
            return StatusCode(500, ApiResponse<BusStop>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 删除站点
    /// </summary>
    /// <param name="id">站点ID</param>
    [HttpDelete("{id}")]
    public async Task<ActionResult<ApiResponse>> DeleteStop(int id)
    {
        try
        {
            await _stopService.DeleteStopAsync(id);
            return Ok(ApiResponse.Ok("站点删除成功"));
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ApiResponse.Fail(ex.Message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除站点失败，站点ID: {StopId}", id);
            return StatusCode(500, ApiResponse.Fail("服务器内部错误"));
        }
    }
}

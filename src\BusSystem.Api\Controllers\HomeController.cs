using Microsoft.AspNetCore.Mvc;
using BusSystem.Core.Services;
using BusSystem.Shared.Models.Common;
using BusSystem.Shared.Enums;

namespace BusSystem.Api.Controllers;

/// <summary>
/// 首页API控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class HomeController : ControllerBase
{
    private readonly IHomePageService _homePageService;
    private readonly ILogger<HomeController> _logger;

    public HomeController(IHomePageService homePageService, ILogger<HomeController> logger)
    {
        _homePageService = homePageService;
        _logger = logger;
    }

    /// <summary>
    /// 获取附近站点及实时信息（首页主接口）
    /// </summary>
    /// <param name="longitude">经度</param>
    /// <param name="latitude">纬度</param>
    /// <param name="radius">搜索半径（米，默认500米）</param>
    /// <param name="coordinateSystem">返回的坐标系（默认GCJ02，适用于高德、腾讯地图）</param>
    [HttpGet("nearby-stops")]
    public async Task<ActionResult<ApiResponse<object>>> GetNearbyStopsWithRealtimeInfo(
        [FromQuery] double longitude,
        [FromQuery] double latitude,
        [FromQuery] double radius = 500,
        [FromQuery] CoordinateSystem coordinateSystem = CoordinateSystem.GCJ02)
    {
        try
        {
            // 验证坐标范围
            if (longitude < -180 || longitude > 180 || latitude < -90 || latitude > 90)
            {
                return BadRequest(ApiResponse<object>.Fail("坐标参数无效"));
            }

            // 限制搜索半径
            if (radius <= 0 || radius > 2000) radius = 500;

            var result = await _homePageService.GetNearbyStopsWithRealtimeInfoAsync(longitude, latitude, radius);
            return Ok(ApiResponse<object>.Ok(result));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取附近站点实时信息失败，位置: ({Longitude}, {Latitude})", longitude, latitude);
            return StatusCode(500, ApiResponse<object>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取站点详细信息及到站预测
    /// </summary>
    /// <param name="stopId">站点ID</param>
    [HttpGet("stop/{stopId}/detail")]
    public async Task<ActionResult<ApiResponse<object>>> GetStopDetailWithPredictions(int stopId)
    {
        try
        {
            var result = await _homePageService.GetStopDetailWithPredictionsAsync(stopId);
            return Ok(ApiResponse<object>.Ok(result));
        }
        catch (InvalidOperationException ex)
        {
            return NotFound(ApiResponse<object>.Fail(ex.Message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取站点详细信息失败，站点ID: {StopId}", stopId);
            return StatusCode(500, ApiResponse<object>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 搜索站点或线路（首页搜索功能）
    /// </summary>
    /// <param name="keyword">搜索关键词</param>
    /// <param name="longitude">用户经度（可选，用于距离排序）</param>
    /// <param name="latitude">用户纬度（可选，用于距离排序）</param>
    [HttpGet("search")]
    public async Task<ActionResult<ApiResponse<object>>> SearchStopsAndLines(
        [FromQuery] string keyword,
        [FromQuery] double? longitude = null,
        [FromQuery] double? latitude = null)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(keyword))
            {
                return BadRequest(ApiResponse<object>.Fail("搜索关键词不能为空"));
            }

            // 验证坐标范围（如果提供）
            if (longitude.HasValue && latitude.HasValue)
            {
                if (longitude < -180 || longitude > 180 || latitude < -90 || latitude > 90)
                {
                    return BadRequest(ApiResponse<object>.Fail("坐标参数无效"));
                }
            }

            var result = await _homePageService.SearchStopsAndLinesAsync(keyword, longitude, latitude);
            return Ok(ApiResponse<object>.Ok(result));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "搜索站点和线路失败，关键词: {Keyword}", keyword);
            return StatusCode(500, ApiResponse<object>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取首页推荐信息
    /// </summary>
    /// <param name="longitude">经度</param>
    /// <param name="latitude">纬度</param>
    [HttpGet("recommendations")]
    public async Task<ActionResult<ApiResponse<object>>> GetRecommendations(
        [FromQuery] double longitude,
        [FromQuery] double latitude)
    {
        try
        {
            // 验证坐标范围
            if (longitude < -180 || longitude > 180 || latitude < -90 || latitude > 90)
            {
                return BadRequest(ApiResponse<object>.Fail("坐标参数无效"));
            }

            // 获取附近站点（较小半径）
            var nearbyInfo = await _homePageService.GetNearbyStopsWithRealtimeInfoAsync(longitude, latitude, 300);

            // 构建推荐信息
            var recommendations = new
            {
                UserLocation = new { Longitude = longitude, Latitude = latitude },
                NearbyStops = nearbyInfo,
                QuickActions = new[]
                {
                    new { Action = "nearby_stops", Title = "附近站点", Icon = "location" },
                    new { Action = "search", Title = "搜索线路", Icon = "search" },
                    new { Action = "favorites", Title = "收藏夹", Icon = "heart" },
                    new { Action = "history", Title = "历史记录", Icon = "history" }
                },
                Tips = new[]
                {
                    "点击站点查看详细到站信息",
                    "长按站点可添加到收藏夹",
                    "下拉刷新获取最新数据"
                }
            };

            return Ok(ApiResponse<object>.Ok(recommendations));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取首页推荐信息失败，位置: ({Longitude}, {Latitude})", longitude, latitude);
            return StatusCode(500, ApiResponse<object>.Fail("服务器内部错误"));
        }
    }
}

[2025-08-20 09:00:20.495 +08:00 INF] Now listening on: http://localhost:5205 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-20 09:00:20.519 +08:00 INF] Application started. Press Ctrl+C to shut down. {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-20 09:00:20.520 +08:00 INF] Hosting environment: Development {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-20 09:00:20.521 +08:00 INF] Content root path: E:\Coding\Solution\实时公交\src\BusSystem.Api {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-20 09:00:42.152 +08:00 WRN] Failed to determine the https port for redirect. {"EventId":{"Id":3,"Name":"FailedToDeterminePort"},"SourceContext":"Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware","RequestId":"0HNEVGJU5TD0Q:00000001","RequestPath":"/api/health/detailed","ConnectionId":"0HNEVGJU5TD0Q"}
[2025-08-20 09:00:50.807 +08:00 ERR] HTTP GET /api/health/detailed responded 500 in 8659.3788 ms {"SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware"}
StackExchange.Redis.RedisConnectionException: It was not possible to connect to the redis server(s). Error connecting right now. To allow this multiplexer to continue retrying until it's able to connect, use abortConnect=false in your connection string or AbortOnConnectFail=false; in your code.
   at StackExchange.Redis.ConnectionMultiplexer.ConnectImpl(ConfigurationOptions configuration, TextWriter writer, Nullable`1 serverType, EndPointCollection endpoints) in /_/src/StackExchange.Redis/ConnectionMultiplexer.cs:line 723
   at StackExchange.Redis.ConnectionMultiplexer.Connect(ConfigurationOptions configuration, TextWriter log) in /_/src/StackExchange.Redis/ConnectionMultiplexer.cs:line 685
   at StackExchange.Redis.ConnectionMultiplexer.Connect(String configuration, TextWriter log) in /_/src/StackExchange.Redis/ConnectionMultiplexer.cs:line 663
   at Program.<>c__DisplayClass0_0.<<Main>$>b__3(IServiceProvider provider) in E:\Coding\Solution\实时公交\src\BusSystem.Api\Program.cs:line 36
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method12(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
[2025-08-20 09:00:50.917 +08:00 ERR] An unhandled exception has occurred while executing the request. {"EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNEVGJU5TD0Q:00000001","RequestPath":"/api/health/detailed","ConnectionId":"0HNEVGJU5TD0Q"}
StackExchange.Redis.RedisConnectionException: It was not possible to connect to the redis server(s). Error connecting right now. To allow this multiplexer to continue retrying until it's able to connect, use abortConnect=false in your connection string or AbortOnConnectFail=false; in your code.
   at StackExchange.Redis.ConnectionMultiplexer.ConnectImpl(ConfigurationOptions configuration, TextWriter writer, Nullable`1 serverType, EndPointCollection endpoints) in /_/src/StackExchange.Redis/ConnectionMultiplexer.cs:line 723
   at StackExchange.Redis.ConnectionMultiplexer.Connect(ConfigurationOptions configuration, TextWriter log) in /_/src/StackExchange.Redis/ConnectionMultiplexer.cs:line 685
   at StackExchange.Redis.ConnectionMultiplexer.Connect(String configuration, TextWriter log) in /_/src/StackExchange.Redis/ConnectionMultiplexer.cs:line 663
   at Program.<>c__DisplayClass0_0.<<Main>$>b__3(IServiceProvider provider) in E:\Coding\Solution\实时公交\src\BusSystem.Api\Program.cs:line 36
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method12(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
[2025-08-20 09:01:12.450 +08:00 INF] 详细健康检查完成 - 整体状态: Healthy {"SourceContext":"BusSystem.Api.Controllers.HealthController","ActionId":"2fff7913-f0d1-4d2a-829e-9efdd148fa31","ActionName":"BusSystem.Api.Controllers.HealthController.GetDetailed (BusSystem.Api)","RequestId":"0HNEVGJU5TD0R:00000001","RequestPath":"/api/health/detailed","ConnectionId":"0HNEVGJU5TD0R"}
[2025-08-20 09:01:12.494 +08:00 INF] HTTP GET /api/health/detailed responded 200 in 96.3511 ms {"SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware"}
[2025-08-20 09:01:20.231 +08:00 ERR] Failed executing DbCommand (32ms) [Parameters=[p0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']

            SELECT DISTINCT ON (vp.vehicle_id) vp.*
            FROM vehicle_positions vp
            INNER JOIN vehicles v ON vp.vehicle_id = v.id
            WHERE v.line_id = @p0
            ORDER BY vp.vehicle_id, vp.timestamp DESC {"EventId":{"Id":20102,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandError"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","ActionId":"a9d43c3b-ad97-4bc5-8d94-b35ab32595d5","ActionName":"BusSystem.Api.Controllers.RealtimeController.GetLineRealtimeStats (BusSystem.Api)","RequestId":"0HNEVGJU5TD0S:00000001","RequestPath":"/api/realtime/line/1/stats","ConnectionId":"0HNEVGJU5TD0S"}
[2025-08-20 09:01:20.251 +08:00 ERR] An exception occurred while iterating over the results of a query for context type 'BusSystem.Infrastructure.Data.TimescaleDbContext'.
Npgsql.PostgresException (0x80004005): 42P01: relation "vehicles" does not exist

POSITION: 118
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42P01
    MessageText: relation "vehicles" does not exist
    Position: 118
    File: parse_relation.c
    Line: 1392
    Routine: parserOpenTable {"EventId":{"Id":10100,"Name":"Microsoft.EntityFrameworkCore.Query.QueryIterationFailed"},"SourceContext":"Microsoft.EntityFrameworkCore.Query","ActionId":"a9d43c3b-ad97-4bc5-8d94-b35ab32595d5","ActionName":"BusSystem.Api.Controllers.RealtimeController.GetLineRealtimeStats (BusSystem.Api)","RequestId":"0HNEVGJU5TD0S:00000001","RequestPath":"/api/realtime/line/1/stats","ConnectionId":"0HNEVGJU5TD0S"}
Npgsql.PostgresException (0x80004005): 42P01: relation "vehicles" does not exist

POSITION: 118
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42P01
    MessageText: relation "vehicles" does not exist
    Position: 118
    File: parse_relation.c
    Line: 1392
    Routine: parserOpenTable
[2025-08-20 09:01:20.261 +08:00 ERR] 获取线路实时位置失败，线路ID: 1 {"SourceContext":"BusSystem.Core.Services.RealtimeService","ActionId":"a9d43c3b-ad97-4bc5-8d94-b35ab32595d5","ActionName":"BusSystem.Api.Controllers.RealtimeController.GetLineRealtimeStats (BusSystem.Api)","RequestId":"0HNEVGJU5TD0S:00000001","RequestPath":"/api/realtime/line/1/stats","ConnectionId":"0HNEVGJU5TD0S"}
Npgsql.PostgresException (0x80004005): 42P01: relation "vehicles" does not exist

POSITION: 118
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at BusSystem.Infrastructure.Repositories.VehiclePositionRepository.GetLineVehiclePositionsAsync(Int32 lineId) in E:\Coding\Solution\实时公交\src\BusSystem.Infrastructure\Repositories\VehiclePositionRepository.cs:line 70
   at BusSystem.Core.Services.RealtimeService.GetLineRealtimePositionsAsync(Int32 lineId) in E:\Coding\Solution\实时公交\src\BusSystem.Core\Services\RealtimeService.cs:line 137
  Exception data:
    Severity: ERROR
    SqlState: 42P01
    MessageText: relation "vehicles" does not exist
    Position: 118
    File: parse_relation.c
    Line: 1392
    Routine: parserOpenTable
[2025-08-20 09:01:20.269 +08:00 ERR] 获取线路实时统计失败，线路ID: 1 {"SourceContext":"BusSystem.Core.Services.RealtimeService","ActionId":"a9d43c3b-ad97-4bc5-8d94-b35ab32595d5","ActionName":"BusSystem.Api.Controllers.RealtimeController.GetLineRealtimeStats (BusSystem.Api)","RequestId":"0HNEVGJU5TD0S:00000001","RequestPath":"/api/realtime/line/1/stats","ConnectionId":"0HNEVGJU5TD0S"}
Npgsql.PostgresException (0x80004005): 42P01: relation "vehicles" does not exist

POSITION: 118
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at BusSystem.Infrastructure.Repositories.VehiclePositionRepository.GetLineVehiclePositionsAsync(Int32 lineId) in E:\Coding\Solution\实时公交\src\BusSystem.Infrastructure\Repositories\VehiclePositionRepository.cs:line 70
   at BusSystem.Core.Services.RealtimeService.GetLineRealtimePositionsAsync(Int32 lineId) in E:\Coding\Solution\实时公交\src\BusSystem.Core\Services\RealtimeService.cs:line 137
   at BusSystem.Core.Services.RealtimeService.GetLineRealtimeStatsAsync(Int32 lineId) in E:\Coding\Solution\实时公交\src\BusSystem.Core\Services\RealtimeService.cs:line 275
  Exception data:
    Severity: ERROR
    SqlState: 42P01
    MessageText: relation "vehicles" does not exist
    Position: 118
    File: parse_relation.c
    Line: 1392
    Routine: parserOpenTable
[2025-08-20 09:01:20.278 +08:00 ERR] 获取线路实时统计失败，线路ID: 1 {"SourceContext":"BusSystem.Api.Controllers.RealtimeController","ActionId":"a9d43c3b-ad97-4bc5-8d94-b35ab32595d5","ActionName":"BusSystem.Api.Controllers.RealtimeController.GetLineRealtimeStats (BusSystem.Api)","RequestId":"0HNEVGJU5TD0S:00000001","RequestPath":"/api/realtime/line/1/stats","ConnectionId":"0HNEVGJU5TD0S"}
Npgsql.PostgresException (0x80004005): 42P01: relation "vehicles" does not exist

POSITION: 118
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.FromSqlQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at BusSystem.Infrastructure.Repositories.VehiclePositionRepository.GetLineVehiclePositionsAsync(Int32 lineId) in E:\Coding\Solution\实时公交\src\BusSystem.Infrastructure\Repositories\VehiclePositionRepository.cs:line 70
   at BusSystem.Core.Services.RealtimeService.GetLineRealtimePositionsAsync(Int32 lineId) in E:\Coding\Solution\实时公交\src\BusSystem.Core\Services\RealtimeService.cs:line 137
   at BusSystem.Core.Services.RealtimeService.GetLineRealtimeStatsAsync(Int32 lineId) in E:\Coding\Solution\实时公交\src\BusSystem.Core\Services\RealtimeService.cs:line 275
   at BusSystem.Api.Controllers.RealtimeController.GetLineRealtimeStats(Int32 lineId) in E:\Coding\Solution\实时公交\src\BusSystem.Api\Controllers\RealtimeController.cs:line 215
  Exception data:
    Severity: ERROR
    SqlState: 42P01
    MessageText: relation "vehicles" does not exist
    Position: 118
    File: parse_relation.c
    Line: 1392
    Routine: parserOpenTable
[2025-08-20 09:01:20.288 +08:00 ERR] HTTP GET /api/realtime/line/1/stats responded 500 in 1344.3122 ms {"SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware"}
[2025-08-20 09:01:26.781 +08:00 INF] HTTP GET /api/realtime/nearby-vehicles responded 200 in 51.8204 ms {"SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware"}
[2025-08-20 09:08:24.216 +08:00 INF] Now listening on: http://localhost:5205 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-20 09:08:24.242 +08:00 INF] Application started. Press Ctrl+C to shut down. {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-20 09:08:24.243 +08:00 INF] Hosting environment: Development {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-20 09:08:24.244 +08:00 INF] Content root path: E:\Coding\Solution\实时公交\src\BusSystem.Api {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-20 09:08:46.723 +08:00 WRN] Failed to determine the https port for redirect. {"EventId":{"Id":3,"Name":"FailedToDeterminePort"},"SourceContext":"Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware","RequestId":"0HNEVGOEL1AEA:00000001","RequestPath":"/api/home/<USER>","ConnectionId":"0HNEVGOEL1AEA"}
[2025-08-20 09:08:48.497 +08:00 INF] HTTP GET /api/home/<USER>"SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware"}
[2025-08-20 09:17:29.758 +08:00 INF] Now listening on: http://localhost:5205 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-20 09:17:29.791 +08:00 INF] Application started. Press Ctrl+C to shut down. {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-20 09:17:29.792 +08:00 INF] Hosting environment: Development {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-20 09:17:29.794 +08:00 INF] Content root path: E:\Coding\Solution\实时公交\src\BusSystem.Api {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-20 09:17:52.254 +08:00 WRN] Failed to determine the https port for redirect. {"EventId":{"Id":3,"Name":"FailedToDeterminePort"},"SourceContext":"Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware","RequestId":"0HNEVGTH7IURL:00000001","RequestPath":"/api/notification/stats","ConnectionId":"0HNEVGTH7IURL"}
[2025-08-20 09:17:52.299 +08:00 INF] HTTP GET /api/notification/stats responded 200 in 44.6317 ms {"SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware"}
[2025-08-20 09:17:57.912 +08:00 INF] HTTP GET /api/notification/guide responded 200 in 12.3243 ms {"SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware"}
[2025-08-20 10:47:56.091 +08:00 INF] Now listening on: http://localhost:5205 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-20 10:47:56.115 +08:00 INF] Application started. Press Ctrl+C to shut down. {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-20 10:47:56.117 +08:00 INF] Hosting environment: Development {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-20 10:47:56.118 +08:00 INF] Content root path: E:\Coding\Solution\实时公交\src\BusSystem.Api {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-20 10:48:28.131 +08:00 WRN] Failed to determine the https port for redirect. {"EventId":{"Id":3,"Name":"FailedToDeterminePort"},"SourceContext":"Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware","RequestId":"0HNEVIG5834QI:00000001","RequestPath":"/","ConnectionId":"0HNEVIG5834QI"}
[2025-08-20 10:48:28.136 +08:00 INF] HTTP GET / responded 404 in 6.0384 ms {"SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware"}
[2025-08-20 10:49:00.808 +08:00 INF] HTTP GET /api/notification/stats responded 200 in 31.7159 ms {"SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware"}
[2025-08-20 10:49:00.863 +08:00 INF] HTTP GET /favicon.ico responded 404 in 0.1280 ms {"SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware"}
[2025-08-20 11:09:39.082 +08:00 INF] HTTP GET /api/Lines responded 200 in 1214.1936 ms {"SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware"}
[2025-08-20 11:32:21.261 +08:00 INF] Application is shutting down... {"SourceContext":"Microsoft.Hosting.Lifetime"}

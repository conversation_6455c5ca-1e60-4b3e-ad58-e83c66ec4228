using Microsoft.EntityFrameworkCore;
using BusSystem.Core.Entities;
using BusSystem.Core.Interfaces;
using BusSystem.Infrastructure.Data;

namespace BusSystem.Infrastructure.Repositories;

/// <summary>
/// 车辆Repository实现
/// </summary>
public class VehicleRepository : Repository<Vehicle>, IVehicleRepository
{
    public VehicleRepository(BusSystemDbContext context) : base(context)
    {
    }

    public async Task<Vehicle?> GetByVehicleNumberAsync(string vehicleNumber)
    {
        return await _dbSet
            .FirstOrDefaultAsync(x => x.VehicleNumber == vehicleNumber);
    }

    public async Task<Vehicle?> GetByPlateNumberAsync(string plateNumber)
    {
        return await _dbSet
            .FirstOrDefaultAsync(x => x.PlateNumber == plateNumber);
    }

    public async Task<IEnumerable<Vehicle>> GetByLineIdAsync(int lineId)
    {
        return await _dbSet
            .Where(x => x.LineId == lineId)
            .OrderBy(x => x.VehicleNumber)
            .ToListAsync();
    }

    public async Task<Vehicle?> GetWithLineAsync(int vehicleId)
    {
        return await _dbSet
            .Include(x => x.Line)
            .FirstOrDefaultAsync(x => x.Id == vehicleId);
    }

    public async Task<IEnumerable<Vehicle>> GetActiveAsync()
    {
        return await _dbSet
            .Where(x => x.Status == 1)
            .OrderBy(x => x.VehicleNumber)
            .ToListAsync();
    }

    public async Task<IEnumerable<Vehicle>> GetByTypeAsync(int vehicleType)
    {
        return await _dbSet
            .Where(x => x.VehicleType == vehicleType)
            .OrderBy(x => x.VehicleNumber)
            .ToListAsync();
    }

    public async Task<IEnumerable<Vehicle>> GetByCompanyAsync(int companyId)
    {
        return await _dbSet
            .Where(x => x.CompanyId == companyId)
            .OrderBy(x => x.VehicleNumber)
            .ToListAsync();
    }
}

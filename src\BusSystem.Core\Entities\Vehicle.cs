namespace BusSystem.Core.Entities;

/// <summary>
/// 车辆实体
/// </summary>
public class Vehicle : BaseEntity
{
    /// <summary>
    /// 车辆编号
    /// </summary>
    public string VehicleNumber { get; set; } = string.Empty;
    
    /// <summary>
    /// 车牌号
    /// </summary>
    public string? PlateNumber { get; set; }
    
    /// <summary>
    /// 所属线路ID
    /// </summary>
    public int LineId { get; set; }
    
    /// <summary>
    /// 车辆类型：1-普通公交，2-BRT，3-电车
    /// </summary>
    public int VehicleType { get; set; } = 1;
    
    /// <summary>
    /// 载客容量
    /// </summary>
    public int? Capacity { get; set; }
    
    /// <summary>
    /// 车辆状态：0-停运，1-运营中，2-维修中
    /// </summary>
    public int Status { get; set; } = 1;
    
    /// <summary>
    /// 运营公司ID
    /// </summary>
    public int? CompanyId { get; set; }
    
    /// <summary>
    /// 导航属性：所属线路
    /// </summary>
    public virtual BusLine Line { get; set; } = null!;
}

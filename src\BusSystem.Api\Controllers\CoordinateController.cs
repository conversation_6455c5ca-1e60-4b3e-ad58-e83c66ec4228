using Microsoft.AspNetCore.Mvc;
using BusSystem.Core.Services;
using BusSystem.Shared.Models.Common;
using BusSystem.Shared.Enums;

namespace BusSystem.Api.Controllers;

/// <summary>
/// 坐标系转换控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class CoordinateController : ControllerBase
{
    private readonly ICoordinateTransformService _coordinateTransformService;
    private readonly ILogger<CoordinateController> _logger;

    public CoordinateController(ICoordinateTransformService coordinateTransformService, ILogger<CoordinateController> logger)
    {
        _coordinateTransformService = coordinateTransformService;
        _logger = logger;
    }

    /// <summary>
    /// 坐标系转换
    /// </summary>
    /// <param name="longitude">经度</param>
    /// <param name="latitude">纬度</param>
    /// <param name="from">源坐标系</param>
    /// <param name="to">目标坐标系</param>
    [HttpGet("transform")]
    public ActionResult<ApiResponse<object>> TransformCoordinate(
        [FromQuery] double longitude,
        [FromQuery] double latitude,
        [FromQuery] CoordinateSystem from = CoordinateSystem.WGS84,
        [FromQuery] CoordinateSystem to = CoordinateSystem.GCJ02)
    {
        try
        {
            // 验证坐标范围
            if (longitude < -180 || longitude > 180 || latitude < -90 || latitude > 90)
            {
                return BadRequest(ApiResponse<object>.Fail("坐标参数无效"));
            }

            var result = _coordinateTransformService.TransformForMapType(longitude, latitude, from, to);
            
            var response = new
            {
                Original = new { Longitude = longitude, Latitude = latitude, CoordinateSystem = from.ToString() },
                Transformed = new { Longitude = result.longitude, Latitude = result.latitude, CoordinateSystem = to.ToString() },
                IsInChina = _coordinateTransformService.IsInChina(longitude, latitude)
            };

            return Ok(ApiResponse<object>.Ok(response));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "坐标转换失败，经度: {Longitude}, 纬度: {Latitude}, 从: {From}, 到: {To}", 
                longitude, latitude, from, to);
            return StatusCode(500, ApiResponse<object>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取多坐标系位置信息
    /// </summary>
    /// <param name="longitude">经度（WGS84）</param>
    /// <param name="latitude">纬度（WGS84）</param>
    [HttpGet("multi-coordinate")]
    public ActionResult<ApiResponse<LocationResponse>> GetMultiCoordinateLocation(
        [FromQuery] double longitude,
        [FromQuery] double latitude)
    {
        try
        {
            // 验证坐标范围
            if (longitude < -180 || longitude > 180 || latitude < -90 || latitude > 90)
            {
                return BadRequest(ApiResponse<LocationResponse>.Fail("坐标参数无效"));
            }

            var locationResponse = _coordinateTransformService.CreateLocationResponse(longitude, latitude);
            
            return Ok(ApiResponse<LocationResponse>.Ok(locationResponse));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取多坐标系位置失败，经度: {Longitude}, 纬度: {Latitude}", longitude, latitude);
            return StatusCode(500, ApiResponse<LocationResponse>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 批量坐标转换
    /// </summary>
    /// <param name="request">批量转换请求</param>
    [HttpPost("batch-transform")]
    public ActionResult<ApiResponse<object>> BatchTransformCoordinates([FromBody] BatchTransformRequest request)
    {
        try
        {
            if (request?.Coordinates == null || !request.Coordinates.Any())
            {
                return BadRequest(ApiResponse<object>.Fail("坐标列表不能为空"));
            }

            if (request.Coordinates.Count() > 1000)
            {
                return BadRequest(ApiResponse<object>.Fail("批量转换最多支持1000个坐标点"));
            }

            var coordinates = request.Coordinates.Select(c => (c.Longitude, c.Latitude));
            var transformedCoordinates = _coordinateTransformService.BatchTransform(coordinates, request.From, request.To);

            var results = transformedCoordinates.Select((coord, index) => new
            {
                Index = index,
                Original = request.Coordinates.ElementAt(index),
                Transformed = new { Longitude = coord.longitude, Latitude = coord.latitude }
            });

            var response = new
            {
                From = request.From.ToString(),
                To = request.To.ToString(),
                TotalCount = results.Count(),
                Results = results
            };

            return Ok(ApiResponse<object>.Ok(response));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量坐标转换失败");
            return StatusCode(500, ApiResponse<object>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 判断坐标是否在中国境内
    /// </summary>
    /// <param name="longitude">经度</param>
    /// <param name="latitude">纬度</param>
    [HttpGet("is-in-china")]
    public ActionResult<ApiResponse<object>> IsInChina([FromQuery] double longitude, [FromQuery] double latitude)
    {
        try
        {
            // 验证坐标范围
            if (longitude < -180 || longitude > 180 || latitude < -90 || latitude > 90)
            {
                return BadRequest(ApiResponse<object>.Fail("坐标参数无效"));
            }

            var isInChina = _coordinateTransformService.IsInChina(longitude, latitude);
            
            var response = new
            {
                Longitude = longitude,
                Latitude = latitude,
                IsInChina = isInChina,
                Message = isInChina ? "坐标在中国境内，需要进行坐标系转换" : "坐标在中国境外，无需坐标系转换"
            };

            return Ok(ApiResponse<object>.Ok(response));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "判断坐标是否在中国境内失败，经度: {Longitude}, 纬度: {Latitude}", longitude, latitude);
            return StatusCode(500, ApiResponse<object>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取坐标系转换指南
    /// </summary>
    [HttpGet("guide")]
    public ActionResult<ApiResponse<object>> GetCoordinateGuide()
    {
        var guide = new
        {
            CoordinateSystems = new
            {
                WGS84 = new
                {
                    Name = "WGS84",
                    Description = "GPS原始坐标系，国际标准坐标系",
                    Usage = "GPS设备、国际地图服务",
                    Note = "调度系统通常使用此坐标系"
                },
                GCJ02 = new
                {
                    Name = "GCJ02",
                    Description = "国测局坐标系（火星坐标）",
                    Usage = "高德地图、腾讯地图、谷歌中国地图",
                    Note = "中国法律要求的加密坐标系"
                },
                BD09 = new
                {
                    Name = "BD09",
                    Description = "百度坐标系",
                    Usage = "百度地图",
                    Note = "在GCJ02基础上再次加密"
                }
            },
            TransformationChain = new[]
            {
                "WGS84 → GCJ02 → BD09",
                "BD09 → GCJ02 → WGS84"
            },
            ApiExamples = new
            {
                SingleTransform = "/api/coordinate/transform?longitude=116.3974&latitude=39.9093&from=WGS84&to=GCJ02",
                MultiCoordinate = "/api/coordinate/multi-coordinate?longitude=116.3974&latitude=39.9093",
                BatchTransform = "POST /api/coordinate/batch-transform",
                IsInChina = "/api/coordinate/is-in-china?longitude=116.3974&latitude=39.9093"
            },
            BestPractices = new[]
            {
                "后端统一使用WGS84存储坐标",
                "前端根据地图类型请求对应坐标系",
                "中国境外坐标无需转换",
                "批量转换提高性能"
            }
        };

        return Ok(ApiResponse<object>.Ok(guide));
    }
}

/// <summary>
/// 批量转换请求
/// </summary>
public class BatchTransformRequest
{
    public CoordinateSystem From { get; set; } = CoordinateSystem.WGS84;
    public CoordinateSystem To { get; set; } = CoordinateSystem.GCJ02;
    public IEnumerable<CoordinatePoint> Coordinates { get; set; } = new List<CoordinatePoint>();
}

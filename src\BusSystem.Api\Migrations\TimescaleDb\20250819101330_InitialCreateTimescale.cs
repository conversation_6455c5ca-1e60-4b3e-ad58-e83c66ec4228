﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace BusSystem.Api.Migrations.TimescaleDb
{
    /// <inheritdoc />
    public partial class InitialCreateTimescale : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "vehicle_positions",
                columns: table => new
                {
                    timestamp = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    vehicle_id = table.Column<int>(type: "integer", nullable: false),
                    latitude = table.Column<double>(type: "double precision", nullable: false),
                    longitude = table.Column<double>(type: "double precision", nullable: false),
                    speed = table.Column<double>(type: "double precision", nullable: true),
                    direction = table.Column<double>(type: "double precision", nullable: true),
                    accuracy = table.Column<double>(type: "double precision", nullable: true),
                    current_stop_id = table.Column<int>(type: "integer", nullable: true),
                    next_stop_id = table.Column<int>(type: "integer", nullable: true),
                    status = table.Column<int>(type: "integer", nullable: false, defaultValue: 1)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_vehicle_positions", x => new { x.timestamp, x.vehicle_id });
                });

            migrationBuilder.CreateIndex(
                name: "idx_vehicle_positions_timestamp",
                table: "vehicle_positions",
                column: "timestamp");

            migrationBuilder.CreateIndex(
                name: "idx_vehicle_positions_vehicle_id",
                table: "vehicle_positions",
                column: "vehicle_id");

            migrationBuilder.CreateIndex(
                name: "idx_vehicle_positions_vehicle_time",
                table: "vehicle_positions",
                columns: new[] { "vehicle_id", "timestamp" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "vehicle_positions");
        }
    }
}

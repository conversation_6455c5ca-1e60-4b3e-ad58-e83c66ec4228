namespace BusSystem.Core.Services;

/// <summary>
/// 首页服务接口
/// </summary>
public interface IHomePageService
{
    /// <summary>
    /// 获取附近站点及实时信息（首页聚合接口）
    /// </summary>
    Task<object> GetNearbyStopsWithRealtimeInfoAsync(double longitude, double latitude, double radiusMeters = 500);
    
    /// <summary>
    /// 获取站点详细信息及到站预测
    /// </summary>
    Task<object> GetStopDetailWithPredictionsAsync(int stopId);
    
    /// <summary>
    /// 搜索站点或线路（首页搜索）
    /// </summary>
    Task<object> SearchStopsAndLinesAsync(string keyword, double? longitude = null, double? latitude = null);
}

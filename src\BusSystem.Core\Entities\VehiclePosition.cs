namespace BusSystem.Core.Entities;

/// <summary>
/// 车辆位置实体（时序数据，存储在TimescaleDB）
/// </summary>
public class VehiclePosition
{
    /// <summary>
    /// 时间戳（主键之一）
    /// </summary>
    public DateTime Timestamp { get; set; }
    
    /// <summary>
    /// 车辆ID（主键之一）
    /// </summary>
    public int VehicleId { get; set; }
    
    /// <summary>
    /// 纬度
    /// </summary>
    public double Latitude { get; set; }
    
    /// <summary>
    /// 经度
    /// </summary>
    public double Longitude { get; set; }
    
    /// <summary>
    /// 速度（km/h）
    /// </summary>
    public double? Speed { get; set; }
    
    /// <summary>
    /// 方向角（0-360度）
    /// </summary>
    public double? Direction { get; set; }
    
    /// <summary>
    /// GPS精度
    /// </summary>
    public double? Accuracy { get; set; }
    
    /// <summary>
    /// 当前站点ID（如果在站点附近）
    /// </summary>
    public int? CurrentStopId { get; set; }
    
    /// <summary>
    /// 下一站点ID
    /// </summary>
    public int? NextStopId { get; set; }
    
    /// <summary>
    /// 车辆状态：1-正常行驶，2-到站停靠，3-故障停车
    /// </summary>
    public int Status { get; set; } = 1;
}

# 实时公交系统数据库设计文档

## 1. 数据库架构概述

### 1.1 数据库选型（优化后）
- **主数据库**: PostgreSQL 15 + PostGIS (基础数据，地理空间查询)
- **时序数据库**: TimescaleDB (车辆GPS轨迹等时序数据)
- **地理查询缓存**: Redis Geo (高频地理位置查询)
- **通用缓存**: Redis 7.0 (API响应缓存、会话管理)

### 1.2 数据分层架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ PostgreSQL      │    │ TimescaleDB     │    │ Redis Geo       │
│ + PostGIS       │    │ (时序数据)      │    │ (地理查询)      │
│ (基础数据)      │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │ Redis Cache     │
                    │ (通用缓存)      │
                    └─────────────────┘
```

### 1.3 数据分类
- **基础数据**: 线路、站点、车辆等相对稳定的数据 (PostgreSQL + PostGIS)
- **实时数据**: 车辆位置、到站预测等高频变化数据 (Redis Geo + TimescaleDB)
- **历史数据**: GPS轨迹、统计分析等数据 (TimescaleDB)
- **缓存数据**: API响应、会话等临时数据 (Redis)

## 2. PostgreSQL + PostGIS 基础数据设计

### 2.1 基础数据表

#### 2.1.1 线路表 (bus_lines)
```sql
CREATE TABLE bus_lines (
    id SERIAL PRIMARY KEY,
    line_number VARCHAR(20) NOT NULL,
    line_name VARCHAR(100) NOT NULL,
    start_stop_name VARCHAR(100) NOT NULL,
    end_stop_name VARCHAR(100) NOT NULL,
    direction SMALLINT NOT NULL, -- 0:上行 1:下行
    route_geometry GEOMETRY(LINESTRING, 4326), -- 线路几何形状
    operation_start_time TIME,
    operation_end_time TIME,
    peak_interval INTEGER,
    normal_interval INTEGER,
    ticket_price DECIMAL(4,2),
    total_distance DECIMAL(8,2),
    total_stops INTEGER,
    company_id INTEGER,
    status SMALLINT DEFAULT 1, -- 0:停运 1:正常 2:部分停运
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_lines_number ON bus_lines (line_number);
CREATE INDEX idx_lines_status ON bus_lines (status);
CREATE INDEX idx_lines_company ON bus_lines (company_id);
CREATE INDEX idx_lines_geometry ON bus_lines USING GIST (route_geometry);
```

#### 2.1.2 站点表 (bus_stops)
```sql
CREATE TABLE bus_stops (
    id SERIAL PRIMARY KEY,
    stop_name VARCHAR(100) NOT NULL,
    stop_code VARCHAR(20) UNIQUE,
    location GEOMETRY(POINT, 4326) NOT NULL, -- 使用PostGIS几何类型
    address VARCHAR(200),
    district VARCHAR(50),
    facilities JSONB, -- 站点设施信息，使用JSONB提升查询性能
    stop_type SMALLINT DEFAULT 1, -- 1:普通站 2:枢纽站 3:首末站
    status SMALLINT DEFAULT 1, -- 0:停用 1:正常
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_stops_name ON bus_stops (stop_name);
CREATE INDEX idx_stops_code ON bus_stops (stop_code);
CREATE INDEX idx_stops_district ON bus_stops (district);
CREATE INDEX idx_stops_location ON bus_stops USING GIST (location); -- 空间索引
CREATE INDEX idx_stops_facilities ON bus_stops USING GIN (facilities); -- JSONB索引
```

#### 2.1.3 线路站点关联表 (line_stops)
```sql
CREATE TABLE line_stops (
    id SERIAL PRIMARY KEY,
    line_id INTEGER NOT NULL,
    stop_id INTEGER NOT NULL,
    sequence_number INTEGER NOT NULL,
    distance_from_start DECIMAL(8,2),
    estimated_time INTEGER,
    is_key_stop BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    FOREIGN KEY (line_id) REFERENCES bus_lines(id) ON DELETE CASCADE,
    FOREIGN KEY (stop_id) REFERENCES bus_stops(id) ON DELETE CASCADE,
    UNIQUE (line_id, sequence_number)
);

-- 创建索引
CREATE INDEX idx_line_stops_line ON line_stops (line_id);
CREATE INDEX idx_line_stops_stop ON line_stops (stop_id);
```


#### 2.1.4 车辆表 (buses)
```sql
CREATE TABLE buses (
    id SERIAL PRIMARY KEY,
    bus_number VARCHAR(20) NOT NULL,
    line_id INTEGER NOT NULL,
    license_plate VARCHAR(20),
    bus_type VARCHAR(20),
    capacity INTEGER,
    fuel_type VARCHAR(20),
    manufacture_year INTEGER,
    status SMALLINT DEFAULT 1, -- 0:停运 1:运营中 2:维修 3:备用
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    FOREIGN KEY (line_id) REFERENCES bus_lines(id)
);

-- 创建索引
CREATE INDEX idx_buses_number ON buses (bus_number);
CREATE INDEX idx_buses_line ON buses (line_id);
CREATE INDEX idx_buses_status ON buses (status);
```

### 2.2 业务数据表

#### 2.2.1 到站预测表 (arrival_predictions)
```sql
CREATE TABLE arrival_predictions (
    id BIGSERIAL PRIMARY KEY,
    bus_id INTEGER NOT NULL,
    line_id INTEGER NOT NULL,
    stop_id INTEGER NOT NULL,
    predicted_arrival_time TIMESTAMP WITH TIME ZONE NOT NULL,
    stops_remaining INTEGER,
    confidence_level DECIMAL(3,2), -- 预测置信度 0.00-1.00
    prediction_method SMALLINT, -- 1:GPS 2:历史数据 3:机器学习
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    FOREIGN KEY (bus_id) REFERENCES buses(id),
    FOREIGN KEY (line_id) REFERENCES bus_lines(id),
    FOREIGN KEY (stop_id) REFERENCES bus_stops(id)
);

-- 创建索引
CREATE INDEX idx_predictions_bus_stop ON arrival_predictions (bus_id, stop_id);
CREATE INDEX idx_predictions_line_stop ON arrival_predictions (line_id, stop_id);
CREATE INDEX idx_predictions_arrival_time ON arrival_predictions (predicted_arrival_time);
CREATE INDEX idx_predictions_created_at ON arrival_predictions (created_at);

-- 自动清理过期预测数据
CREATE OR REPLACE FUNCTION cleanup_old_predictions() RETURNS void AS $$
BEGIN
    DELETE FROM arrival_predictions
    WHERE created_at < NOW() - INTERVAL '1 hour';
END;
$$ LANGUAGE plpgsql;
```

### 2.3 系统数据表

#### 2.3.1 用户位置记录表 (user_locations)
```sql
CREATE TABLE user_locations (
    id BIGSERIAL PRIMARY KEY,
    session_id VARCHAR(64),
    user_ip INET,
    location GEOMETRY(POINT, 4326),
    accuracy DECIMAL(8,2), -- 定位精度(米)
    location_source SMALLINT, -- 1:GPS 2:网络 3:手动
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_user_locations_session ON user_locations (session_id);
CREATE INDEX idx_user_locations_location ON user_locations USING GIST (location);
CREATE INDEX idx_user_locations_created_at ON user_locations (created_at);
```

#### 2.3.2 搜索记录表 (search_logs)
```sql
CREATE TABLE search_logs (
    id BIGSERIAL PRIMARY KEY,
    session_id VARCHAR(64),
    search_type SMALLINT, -- 1:线路 2:站点
    search_keyword VARCHAR(100),
    search_results_count INTEGER,
    user_ip INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_search_logs_session ON search_logs (session_id);
CREATE INDEX idx_search_logs_keyword ON search_logs (search_keyword);
CREATE INDEX idx_search_logs_type ON search_logs (search_type);
CREATE INDEX idx_search_logs_created_at ON search_logs (created_at);
```

#### 2.3.3 系统配置表 (system_configs)
```sql
CREATE TABLE system_configs (
    id SERIAL PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value TEXT,
    config_type VARCHAR(20) DEFAULT 'string',
    description VARCHAR(200),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_system_configs_key ON system_configs (config_key);
CREATE INDEX idx_system_configs_active ON system_configs (is_active);
```


## 3. TimescaleDB 时序数据设计

### 3.1 车辆GPS轨迹表 (bus_gps_tracks)
```sql
-- 创建时序表（TimescaleDB）
CREATE TABLE bus_gps_tracks (
    time TIMESTAMPTZ NOT NULL,
    bus_id INTEGER NOT NULL,
    line_id INTEGER NOT NULL,
    location GEOMETRY(POINT, 4326) NOT NULL,
    speed REAL,
    direction INTEGER, -- 行驶方向(角度 0-360)
    altitude REAL,
    accuracy REAL, -- GPS精度(米)
    current_stop_id INTEGER,
    next_stop_id INTEGER,
    passenger_count INTEGER,
    status SMALLINT, -- 1:正常 2:延误 3:故障
    device_id VARCHAR(50),
    raw_data JSONB -- 原始GPS数据
);

-- 转换为时序表（按时间分区，每天一个分区）
SELECT create_hypertable('bus_gps_tracks', 'time', chunk_time_interval => INTERVAL '1 day');

-- 创建索引
CREATE INDEX ON bus_gps_tracks (bus_id, time DESC);
CREATE INDEX ON bus_gps_tracks (line_id, time DESC);
CREATE INDEX ON bus_gps_tracks USING GIST (location);
CREATE INDEX ON bus_gps_tracks (time DESC, bus_id) WHERE status != 1; -- 异常状态索引

-- 数据保留策略（保留30天数据）
SELECT add_retention_policy('bus_gps_tracks', INTERVAL '30 days');

-- 数据压缩策略（7天后压缩）
ALTER TABLE bus_gps_tracks SET (
    timescaledb.compress,
    timescaledb.compress_segmentby = 'bus_id, line_id',
    timescaledb.compress_orderby = 'time DESC'
);

SELECT add_compression_policy('bus_gps_tracks', INTERVAL '7 days');
```

### 3.2 车辆状态变更记录表 (bus_status_changes)
```sql
-- 车辆状态变更时序表
CREATE TABLE bus_status_changes (
    time TIMESTAMPTZ NOT NULL,
    bus_id INTEGER NOT NULL,
    line_id INTEGER NOT NULL,
    old_status SMALLINT,
    new_status SMALLINT,
    change_reason VARCHAR(100),
    location GEOMETRY(POINT, 4326),
    operator_id INTEGER
);

-- 转换为时序表
SELECT create_hypertable('bus_status_changes', 'time', chunk_time_interval => INTERVAL '7 days');

-- 创建索引
CREATE INDEX ON bus_status_changes (bus_id, time DESC);
CREATE INDEX ON bus_status_changes (line_id, time DESC);
CREATE INDEX ON bus_status_changes (new_status, time DESC);

-- 数据保留策略（保留90天）
SELECT add_retention_policy('bus_status_changes', INTERVAL '90 days');
```

### 3.3 系统性能指标表 (system_metrics)
```sql
-- 系统性能指标时序表
CREATE TABLE system_metrics (
    time TIMESTAMPTZ NOT NULL,
    metric_name VARCHAR(50) NOT NULL,
    metric_value REAL NOT NULL,
    metric_unit VARCHAR(20),
    service_name VARCHAR(50),
    instance_id VARCHAR(50),
    tags JSONB
);

-- 转换为时序表
SELECT create_hypertable('system_metrics', 'time', chunk_time_interval => INTERVAL '1 hour');

-- 创建索引
CREATE INDEX ON system_metrics (metric_name, time DESC);
CREATE INDEX ON system_metrics (service_name, time DESC);
CREATE INDEX ON system_metrics USING GIN (tags);

-- 数据保留策略（保留7天详细数据）
SELECT add_retention_policy('system_metrics', INTERVAL '7 days');



## 4. Redis Geo 地理位置设计

### 4.1 Redis Geo 数据结构

#### 4.1.1 站点地理位置存储
```bash
# 存储所有公交站点的地理位置
# Key: geo:bus_stops
# 格式: GEOADD key longitude latitude member

GEOADD geo:bus_stops 114.085456 22.547123 "stop:1001"
GEOADD geo:bus_stops 114.086789 22.548456 "stop:1002"
GEOADD geo:bus_stops 114.087123 22.549789 "stop:1003"
```

#### 4.1.2 车辆实时位置存储
```bash
# 存储车辆实时位置（按线路分组）
# Key: geo:buses:{line_id}
# 自动过期时间：300秒

GEOADD geo:buses:2001 114.085456 22.547123 "bus:3001"
EXPIRE geo:buses:2001 300

# 或者统一存储所有车辆
GEOADD geo:all_buses 114.085456 22.547123 "bus:3001:line:2001"
```

#### 4.1.3 地理查询操作
```bash
# 查询附近500米内的站点
GEORADIUS geo:bus_stops 114.085456 22.547123 500 m WITHDIST WITHCOORD ASC COUNT 10

# 查询指定站点附近的车辆
GEORADIUSBYMEMBER geo:all_buses stop:1001 1000 m WITHDIST ASC

# 计算两个站点之间的距离
GEODIST geo:bus_stops stop:1001 stop:1002 m

# 获取站点坐标
GEOPOS geo:bus_stops stop:1001 stop:1002
```

### 4.2 Redis 通用缓存设计

#### 4.2.1 缓存键命名规范
```
# 实时数据缓存
bus:realtime:{line_id}:{direction}          # 线路实时数据
bus:arrival:{stop_id}:{line_id}             # 站点到站预测
line:buses:{line_id}                        # 线路车辆列表

# 基础数据缓存
line:info:{line_id}                         # 线路基础信息
stop:info:{stop_id}                         # 站点基础信息
line:stops:{line_id}:{direction}            # 线路站点列表

# 搜索缓存
search:line:{keyword_hash}                  # 线路搜索结果
search:stop:{keyword_hash}                  # 站点搜索结果

# 用户会话缓存
session:{session_id}                        # 用户会话信息
user:location:{session_id}                  # 用户位置缓存

# 统计缓存
stats:daily:{date}                          # 每日统计数据
stats:popular:lines                         # 热门线路
stats:popular:stops                         # 热门站点
```

#### 4.2.2 缓存策略和TTL设置
```
# Redis Geo TTL设置
geo:bus_stops: 永久存储（基础数据）
geo:buses:{line_id}: 300秒（实时位置）
geo:all_buses: 300秒（全局车辆位置）

# 通用缓存TTL设置
bus:realtime:*: 30秒（实时数据）
bus:arrival:*: 60秒（到站预测）
line:info:*: 3600秒（基础信息）
stop:info:*: 3600秒（站点信息）
search:*: 600秒（搜索结果）
session:*: 1800秒（用户会话）
user:location:*: 300秒（用户位置）

# 缓存更新策略
- 地理位置数据: GPS数据更新时同步更新Redis Geo
- 实时数据: 主动推送更新
- 基础数据: 懒加载 + 定时更新
- 搜索结果: 写入时更新
```

## 5. 数据库优化策略

### 5.1 PostgreSQL 索引优化
```sql
-- 空间索引已在表创建时定义
-- 复合索引优化
CREATE INDEX idx_line_stops_composite ON line_stops (line_id, sequence_number, stop_id);
CREATE INDEX idx_predictions_composite ON arrival_predictions (line_id, stop_id, predicted_arrival_time);

-- 部分索引（只索引活跃数据）
CREATE INDEX idx_active_buses ON buses (line_id, status) WHERE status IN (1, 2);
CREATE INDEX idx_recent_predictions ON arrival_predictions (bus_id, stop_id)
WHERE created_at > NOW() - INTERVAL '1 hour';
```

### 5.2 TimescaleDB 优化策略
```sql
-- 已在表创建时配置：
-- 1. 自动分区（按天）
-- 2. 数据压缩（7天后）
-- 3. 数据保留策略（30天）

-- 连续聚合（预计算常用查询）
CREATE MATERIALIZED VIEW bus_hourly_stats
WITH (timescaledb.continuous) AS
SELECT
    time_bucket('1 hour', time) AS hour,
    bus_id,
    line_id,
    AVG(speed) as avg_speed,
    COUNT(*) as gps_points
FROM bus_gps_tracks
GROUP BY hour, bus_id, line_id;

-- 刷新策略
SELECT add_continuous_aggregate_policy('bus_hourly_stats',
    start_offset => INTERVAL '2 hours',
    end_offset => INTERVAL '1 hour',
    schedule_interval => INTERVAL '1 hour');
```

### 5.3 数据清理策略
```sql
-- PostgreSQL 数据清理
-- 到站预测数据：1小时后自动清理（已在表中定义函数）
-- 用户位置记录：7天后清理
-- 搜索日志：30天后清理

-- TimescaleDB 自动清理
-- GPS轨迹数据：30天后自动删除（已配置保留策略）
-- 状态变更记录：90天后自动删除
-- 系统指标：7天后自动删除
```

## 6. 数据完整性约束

### 6.1 外键约束
- 严格的外键约束确保数据一致性
- 级联删除策略避免孤立数据

### 6.2 数据验证（PostgreSQL）
```sql
-- 地理坐标范围检查（PostGIS会自动验证）
-- 状态值检查
ALTER TABLE bus_lines ADD CONSTRAINT chk_lines_status CHECK (status IN (0,1,2));
ALTER TABLE buses ADD CONSTRAINT chk_buses_status CHECK (status IN (0,1,2,3));

-- 预测置信度检查
ALTER TABLE arrival_predictions ADD CONSTRAINT chk_confidence
CHECK (confidence_level >= 0.0 AND confidence_level <= 1.0);

-- 时间合理性检查
ALTER TABLE arrival_predictions ADD CONSTRAINT chk_future_arrival
CHECK (predicted_arrival_time > created_at);
```

## 7. 性能监控

### 7.1 PostgreSQL 监控
```sql
-- 开启慢查询日志
ALTER SYSTEM SET log_min_duration_statement = 1000; -- 1秒
SELECT pg_reload_conf();

-- 查看慢查询
SELECT query, mean_exec_time, calls
FROM pg_stat_statements
ORDER BY mean_exec_time DESC LIMIT 10;
```

### 7.2 TimescaleDB 监控
```sql
-- 查看分区信息
SELECT * FROM timescaledb_information.chunks
WHERE hypertable_name = 'bus_gps_tracks';

-- 查看压缩状态
SELECT * FROM timescaledb_information.compression_settings;
```

### 7.3 Redis 监控
```bash
# Redis性能监控
redis-cli INFO stats
redis-cli INFO memory
redis-cli GEORADIUS_RO geo:bus_stops 114.085 22.547 1000 m COUNT 1 # 测试地理查询
```

### 7.4 关键指标监控
- **PostgreSQL**: 连接数、查询响应时间、缓存命中率
- **TimescaleDB**: 写入TPS、压缩率、分区数量
- **Redis**: 内存使用率、地理查询QPS、缓存命中率
- **系统**: CPU使用率、磁盘I/O、网络带宽

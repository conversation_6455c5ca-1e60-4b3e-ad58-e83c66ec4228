using System.Net.WebSockets;
using WebSocketState = System.Net.WebSockets.WebSocketState;

namespace BusSystem.Shared.Models.WebSocket;

/// <summary>
/// WebSocket连接信息
/// </summary>
public class WebSocketConnection
{
    public string ConnectionId { get; set; } = string.Empty;
    public System.Net.WebSockets.WebSocket WebSocket { get; set; } = null!;
    public string? UserId { get; set; }
    public DateTime ConnectedAt { get; set; }
    public DateTime LastActivity { get; set; }
    public HashSet<int> SubscribedLines { get; set; } = new();
    public HashSet<int> SubscribedStops { get; set; } = new();
    public List<AreaSubscription> SubscribedAreas { get; set; } = new();
    public string? UserAgent { get; set; }
    public string? IpAddress { get; set; }
}

/// <summary>
/// 区域订阅信息
/// </summary>
public class AreaSubscription
{
    public double Longitude { get; set; }
    public double Latitude { get; set; }
    public double RadiusMeters { get; set; }
    public DateTime SubscribedAt { get; set; }
}

/// <summary>
/// WebSocket消息类型
/// </summary>
public enum WebSocketMessageType
{
    /// <summary>
    /// 连接确认
    /// </summary>
    ConnectionAck,
    
    /// <summary>
    /// 订阅请求
    /// </summary>
    Subscribe,
    
    /// <summary>
    /// 取消订阅
    /// </summary>
    Unsubscribe,
    
    /// <summary>
    /// 车辆位置更新
    /// </summary>
    VehiclePositionUpdate,
    
    /// <summary>
    /// 到站预测更新
    /// </summary>
    ArrivalPredictionUpdate,
    
    /// <summary>
    /// 线路状态更新
    /// </summary>
    LineStatusUpdate,
    
    /// <summary>
    /// 系统通知
    /// </summary>
    SystemNotification,
    
    /// <summary>
    /// 心跳包
    /// </summary>
    Heartbeat,
    
    /// <summary>
    /// 错误消息
    /// </summary>
    Error
}

/// <summary>
/// WebSocket消息
/// </summary>
public class WebSocketMessage
{
    public WebSocketMessageType Type { get; set; }
    public string? MessageId { get; set; }
    public DateTime Timestamp { get; set; }
    public object? Data { get; set; }
    public string? Error { get; set; }
}

/// <summary>
/// 订阅请求
/// </summary>
public class SubscriptionRequest
{
    public string Action { get; set; } = string.Empty; // "subscribe" | "unsubscribe"
    public string Target { get; set; } = string.Empty; // "line" | "stop" | "area"
    public int? LineId { get; set; }
    public int? StopId { get; set; }
    public double? Longitude { get; set; }
    public double? Latitude { get; set; }
    public double? RadiusMeters { get; set; }
}

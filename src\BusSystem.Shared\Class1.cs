﻿namespace BusSystem.Shared.Constants;

/// <summary>
/// 系统常量定义
/// </summary>
public static class SystemConstants
{
    /// <summary>
    /// 默认分页大小
    /// </summary>
    public const int DefaultPageSize = 20;

    /// <summary>
    /// 最大分页大小
    /// </summary>
    public const int MaxPageSize = 100;

    /// <summary>
    /// Redis键前缀
    /// </summary>
    public static class RedisKeys
    {
        public const string BusStops = "bus_stops";
        public const string BusLines = "bus_lines";
        public const string VehiclePositions = "vehicle_positions";
        public const string RealtimeData = "realtime_data";
    }

    /// <summary>
    /// 缓存过期时间（秒）
    /// </summary>
    public static class CacheExpiration
    {
        public const int ShortTerm = 300;    // 5分钟
        public const int MediumTerm = 1800;  // 30分钟
        public const int LongTerm = 3600;    // 1小时
    }
}

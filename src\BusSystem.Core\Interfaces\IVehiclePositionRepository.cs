using BusSystem.Core.Entities;

namespace BusSystem.Core.Interfaces;

/// <summary>
/// 车辆位置Repository接口（时序数据）
/// </summary>
public interface IVehiclePositionRepository
{
    /// <summary>
    /// 添加车辆位置记录
    /// </summary>
    Task<VehiclePosition> AddAsync(VehiclePosition position);
    
    /// <summary>
    /// 批量添加车辆位置记录
    /// </summary>
    Task<IEnumerable<VehiclePosition>> AddRangeAsync(IEnumerable<VehiclePosition> positions);
    
    /// <summary>
    /// 获取车辆最新位置
    /// </summary>
    Task<VehiclePosition?> GetLatestPositionAsync(int vehicleId);
    
    /// <summary>
    /// 获取多个车辆的最新位置
    /// </summary>
    Task<IEnumerable<VehiclePosition>> GetLatestPositionsAsync(IEnumerable<int> vehicleIds);
    
    /// <summary>
    /// 获取线路上所有车辆的最新位置
    /// </summary>
    Task<IEnumerable<VehiclePosition>> GetLineVehiclePositionsAsync(int lineId);
    
    /// <summary>
    /// 获取车辆在指定时间范围内的位置轨迹
    /// </summary>
    Task<IEnumerable<VehiclePosition>> GetVehicleTrajectoryAsync(int vehicleId, DateTime startTime, DateTime endTime);
    
    /// <summary>
    /// 获取附近的车辆位置
    /// </summary>
    Task<IEnumerable<VehiclePosition>> GetNearbyVehiclesAsync(double longitude, double latitude, double radiusMeters);
    
    /// <summary>
    /// 删除指定时间之前的历史数据
    /// </summary>
    Task DeleteOldDataAsync(DateTime beforeTime);
    
    /// <summary>
    /// 获取车辆位置统计信息
    /// </summary>
    Task<object> GetPositionStatsAsync(int vehicleId, DateTime date);
}

using Microsoft.Extensions.Logging;
using BusSystem.Shared.Enums;
using BusSystem.Shared.Models.Common;

namespace BusSystem.Core.Services;

/// <summary>
/// 坐标系转换服务实现
/// </summary>
public class CoordinateTransformService : ICoordinateTransformService
{
    private readonly ILogger<CoordinateTransformService> _logger;
    
    // 坐标转换常量
    private const double PI = Math.PI;
    private const double A = 6378245.0; // 长半轴
    private const double EE = 0.00669342162296594323; // 偏心率平方
    private const double X_PI = PI * 3000.0 / 180.0;

    public CoordinateTransformService(ILogger<CoordinateTransformService> logger)
    {
        _logger = logger;
    }

    public (double longitude, double latitude) Wgs84ToGcj02(double longitude, double latitude)
    {
        if (!IsInChina(longitude, latitude))
        {
            return (longitude, latitude);
        }

        var dLat = TransformLatitude(longitude - 105.0, latitude - 35.0);
        var dLon = TransformLongitude(longitude - 105.0, latitude - 35.0);
        
        var radLat = latitude / 180.0 * PI;
        var magic = Math.Sin(radLat);
        magic = 1 - EE * magic * magic;
        var sqrtMagic = Math.Sqrt(magic);
        
        dLat = (dLat * 180.0) / ((A * (1 - EE)) / (magic * sqrtMagic) * PI);
        dLon = (dLon * 180.0) / (A / sqrtMagic * Math.Cos(radLat) * PI);
        
        var mgLat = latitude + dLat;
        var mgLon = longitude + dLon;
        
        return (mgLon, mgLat);
    }

    public (double longitude, double latitude) Gcj02ToWgs84(double longitude, double latitude)
    {
        if (!IsInChina(longitude, latitude))
        {
            return (longitude, latitude);
        }

        var dLat = TransformLatitude(longitude - 105.0, latitude - 35.0);
        var dLon = TransformLongitude(longitude - 105.0, latitude - 35.0);
        
        var radLat = latitude / 180.0 * PI;
        var magic = Math.Sin(radLat);
        magic = 1 - EE * magic * magic;
        var sqrtMagic = Math.Sqrt(magic);
        
        dLat = (dLat * 180.0) / ((A * (1 - EE)) / (magic * sqrtMagic) * PI);
        dLon = (dLon * 180.0) / (A / sqrtMagic * Math.Cos(radLat) * PI);
        
        var mgLat = latitude - dLat;
        var mgLon = longitude - dLon;
        
        return (mgLon, mgLat);
    }

    public (double longitude, double latitude) Gcj02ToBd09(double longitude, double latitude)
    {
        var z = Math.Sqrt(longitude * longitude + latitude * latitude) + 0.00002 * Math.Sin(latitude * X_PI);
        var theta = Math.Atan2(latitude, longitude) + 0.000003 * Math.Cos(longitude * X_PI);
        
        var bdLon = z * Math.Cos(theta) + 0.0065;
        var bdLat = z * Math.Sin(theta) + 0.006;
        
        return (bdLon, bdLat);
    }

    public (double longitude, double latitude) Bd09ToGcj02(double longitude, double latitude)
    {
        var x = longitude - 0.0065;
        var y = latitude - 0.006;
        var z = Math.Sqrt(x * x + y * y) - 0.00002 * Math.Sin(y * X_PI);
        var theta = Math.Atan2(y, x) - 0.000003 * Math.Cos(x * X_PI);
        
        var gcjLon = z * Math.Cos(theta);
        var gcjLat = z * Math.Sin(theta);
        
        return (gcjLon, gcjLat);
    }

    public (double longitude, double latitude) Wgs84ToBd09(double longitude, double latitude)
    {
        var gcj02 = Wgs84ToGcj02(longitude, latitude);
        return Gcj02ToBd09(gcj02.longitude, gcj02.latitude);
    }

    public (double longitude, double latitude) Bd09ToWgs84(double longitude, double latitude)
    {
        var gcj02 = Bd09ToGcj02(longitude, latitude);
        return Gcj02ToWgs84(gcj02.longitude, gcj02.latitude);
    }

    public (double longitude, double latitude) TransformForMapType(double longitude, double latitude, 
        CoordinateSystem from, CoordinateSystem to)
    {
        if (from == to)
        {
            return (longitude, latitude);
        }

        return (from, to) switch
        {
            (CoordinateSystem.WGS84, CoordinateSystem.GCJ02) => Wgs84ToGcj02(longitude, latitude),
            (CoordinateSystem.WGS84, CoordinateSystem.BD09) => Wgs84ToBd09(longitude, latitude),
            (CoordinateSystem.GCJ02, CoordinateSystem.WGS84) => Gcj02ToWgs84(longitude, latitude),
            (CoordinateSystem.GCJ02, CoordinateSystem.BD09) => Gcj02ToBd09(longitude, latitude),
            (CoordinateSystem.BD09, CoordinateSystem.WGS84) => Bd09ToWgs84(longitude, latitude),
            (CoordinateSystem.BD09, CoordinateSystem.GCJ02) => Bd09ToGcj02(longitude, latitude),
            _ => (longitude, latitude)
        };
    }

    public IEnumerable<(double longitude, double latitude)> BatchTransform(
        IEnumerable<(double longitude, double latitude)> coordinates,
        CoordinateSystem from, CoordinateSystem to)
    {
        return coordinates.Select(coord => TransformForMapType(coord.longitude, coord.latitude, from, to));
    }

    public bool IsInChina(double longitude, double latitude)
    {
        // 简化的中国边界判断
        return longitude >= 72.004 && longitude <= 137.8347 &&
               latitude >= 0.8293 && latitude <= 55.8271;
    }

    public LocationResponse CreateLocationResponse(double longitude, double latitude)
    {
        var gcj02 = Wgs84ToGcj02(longitude, latitude);
        var bd09 = Wgs84ToBd09(longitude, latitude);

        return new LocationResponse
        {
            Wgs84 = new CoordinatePoint { Longitude = longitude, Latitude = latitude },
            Gcj02 = new CoordinatePoint { Longitude = gcj02.longitude, Latitude = gcj02.latitude },
            Bd09 = new CoordinatePoint { Longitude = bd09.longitude, Latitude = bd09.latitude }
        };
    }

    #region 私有方法

    private static double TransformLatitude(double longitude, double latitude)
    {
        var ret = -100.0 + 2.0 * longitude + 3.0 * latitude + 0.2 * latitude * latitude + 
                  0.1 * longitude * latitude + 0.2 * Math.Sqrt(Math.Abs(longitude));
        
        ret += (20.0 * Math.Sin(6.0 * longitude * PI) + 20.0 * Math.Sin(2.0 * longitude * PI)) * 2.0 / 3.0;
        ret += (20.0 * Math.Sin(latitude * PI) + 40.0 * Math.Sin(latitude / 3.0 * PI)) * 2.0 / 3.0;
        ret += (160.0 * Math.Sin(latitude / 12.0 * PI) + 320 * Math.Sin(latitude * PI / 30.0)) * 2.0 / 3.0;
        
        return ret;
    }

    private static double TransformLongitude(double longitude, double latitude)
    {
        var ret = 300.0 + longitude + 2.0 * latitude + 0.1 * longitude * longitude + 
                  0.1 * longitude * latitude + 0.1 * Math.Sqrt(Math.Abs(longitude));
        
        ret += (20.0 * Math.Sin(6.0 * longitude * PI) + 20.0 * Math.Sin(2.0 * longitude * PI)) * 2.0 / 3.0;
        ret += (20.0 * Math.Sin(longitude * PI) + 40.0 * Math.Sin(longitude / 3.0 * PI)) * 2.0 / 3.0;
        ret += (150.0 * Math.Sin(longitude / 12.0 * PI) + 300.0 * Math.Sin(longitude / 30.0 * PI)) * 2.0 / 3.0;
        
        return ret;
    }

    #endregion
}
